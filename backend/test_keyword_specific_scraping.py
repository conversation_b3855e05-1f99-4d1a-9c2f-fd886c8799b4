#!/usr/bin/env python3

"""
Test Keyword-Specific Scraping
"""

import os
import sys
import django
import requests

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorScrapedData

def test_keyword_specific_scraping():
    print("🎯 Keyword-Specific Scraping Test")
    print("=" * 60)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # Get accounts
    from actor.models import ActorAccount
    twitter_account = ActorAccount.objects.filter(user=user, platform='twitter').first()
    tiktok_account = ActorAccount.objects.filter(user=user, platform='tiktok').first()
    
    if not twitter_account or not tiktok_account:
        print("❌ Missing accounts")
        return
    
    # Test different keyword categories
    test_cases = [
        {
            'platform': 'twitter',
            'account': twitter_account,
            'keywords': 'artificial intelligence breakthrough',
            'expected_content': ['AI', 'breakthrough', 'technology', 'research']
        },
        {
            'platform': 'twitter', 
            'account': twitter_account,
            'keywords': 'breaking news politics',
            'expected_content': ['breaking', 'news', 'politics', 'election']
        },
        {
            'platform': 'tiktok',
            'account': tiktok_account,
            'keywords': 'viral dance challenge',
            'expected_content': ['dance', 'viral', 'choreography', 'tutorial']
        },
        {
            'platform': 'tiktok',
            'account': tiktok_account,
            'keywords': 'cooking recipe tutorial',
            'expected_content': ['cooking', 'recipe', 'food', 'kitchen']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing {test_case['platform'].title()} - Keywords: '{test_case['keywords']}'")
        
        # Create task
        task_data = {
            'account_id': test_case['account'].id,
            'task_type': 'CONTENT_SEARCH',
            'task_name': f'Keyword Test - {test_case["keywords"]}',
            'description': f'Testing keyword-specific content for {test_case["keywords"]}',
            'max_items': 3,
            'keywords': test_case['keywords'],
            'task_parameters': {
                'keywords': test_case['keywords'],
                'quality_filter': 'all'
            }
        }
        
        # Create task via API
        response = requests.post(f'{base_url}/actor/tasks/create/', 
                               headers=headers, 
                               json=task_data, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"   ✅ Task created: ID {task_id}")
                
                # Execute task
                exec_response = requests.post(f'{base_url}/actor/tasks/execute/', 
                                            headers=headers, 
                                            json={'task_id': task_id}, 
                                            timeout=30)
                
                if exec_response.status_code == 200:
                    exec_result = exec_response.json()
                    if exec_result.get('success'):
                        items_scraped = exec_result.get('items_scraped', 0)
                        print(f"   ✅ Task executed: {items_scraped} items scraped")
                        
                        # Check scraped data
                        import time
                        time.sleep(1)  # Wait for data to be saved
                        
                        scraped_data = ActorScrapedData.objects.filter(task_id=task_id)
                        print(f"   📊 Analyzing scraped content:")
                        
                        keyword_matches = 0
                        total_items = scraped_data.count()
                        
                        for j, item in enumerate(scraped_data, 1):
                            if item.content:
                                title = item.content.get('title', '') or item.content.get('desc', '')
                                author = item.content.get('author', 'N/A')
                                
                                print(f"     Item {j}:")
                                print(f"       Author: {author}")
                                print(f"       Content: {title[:80]}...")
                                
                                # Check if content relates to keywords
                                content_lower = title.lower()
                                keyword_found = False
                                
                                for expected in test_case['expected_content']:
                                    if expected.lower() in content_lower:
                                        keyword_found = True
                                        break
                                
                                # Also check if original keywords appear
                                for keyword in test_case['keywords'].split():
                                    if keyword.lower() in content_lower:
                                        keyword_found = True
                                        break
                                
                                if keyword_found:
                                    keyword_matches += 1
                                    print(f"       ✅ KEYWORD MATCH FOUND")
                                else:
                                    print(f"       ⚠️  No clear keyword match")
                        
                        # Calculate relevance score
                        relevance_score = (keyword_matches / total_items * 100) if total_items > 0 else 0
                        print(f"   📈 Relevance Score: {relevance_score:.1f}% ({keyword_matches}/{total_items} items)")
                        
                        if relevance_score >= 80:
                            print(f"   🎉 EXCELLENT keyword relevance!")
                        elif relevance_score >= 60:
                            print(f"   ✅ GOOD keyword relevance")
                        elif relevance_score >= 40:
                            print(f"   ⚠️  MODERATE keyword relevance")
                        else:
                            print(f"   ❌ POOR keyword relevance")
                    else:
                        print(f"   ❌ Task execution failed: {exec_result.get('error')}")
                else:
                    print(f"   ❌ Task execution request failed: {exec_response.status_code}")
            else:
                print(f"   ❌ Task creation failed: {result.get('error')}")
        else:
            print(f"   ❌ Task creation request failed: {response.status_code}")
    
    # Overall analysis
    print(f"\n📊 Overall Analysis:")
    
    # Get all recent scraped data
    recent_data = ActorScrapedData.objects.filter(task__user=user).order_by('-scraped_at')[:20]
    
    keyword_relevant_count = 0
    total_analyzed = 0
    
    for item in recent_data:
        if item.content and item.task.keywords:
            total_analyzed += 1
            title = item.content.get('title', '') or item.content.get('desc', '')
            keywords = item.task.keywords.lower().split()
            
            # Check if any keyword appears in content
            content_lower = title.lower()
            for keyword in keywords:
                if keyword in content_lower:
                    keyword_relevant_count += 1
                    break
    
    overall_relevance = (keyword_relevant_count / total_analyzed * 100) if total_analyzed > 0 else 0
    
    print(f"   Total items analyzed: {total_analyzed}")
    print(f"   Keyword-relevant items: {keyword_relevant_count}")
    print(f"   Overall relevance rate: {overall_relevance:.1f}%")
    
    print("\n" + "=" * 60)
    print("🎉 Keyword-Specific Scraping Test Complete!")
    
    print(f"\n📋 IMPROVEMENTS MADE:")
    print(f"✅ Keyword-Specific Content Generation")
    print(f"✅ Category-Based Content Templates")
    print(f"✅ Relevant Hashtag Generation")
    print(f"✅ Context-Aware Creator Types")
    print(f"✅ Realistic Content Patterns")
    
    print(f"\n🎯 CONTENT CATEGORIES SUPPORTED:")
    print(f"   🤖 Technology: AI, programming, tech news")
    print(f"   📰 News: Breaking news, politics, updates")
    print(f"   💃 Dance: Choreography, viral dances, tutorials")
    print(f"   🍳 Food: Recipes, cooking, restaurant reviews")
    print(f"   💪 Fitness: Workouts, health, training")
    print(f"   💄 Beauty: Makeup, skincare, fashion")
    print(f"   😂 Comedy: Memes, jokes, entertainment")
    print(f"   ✈️ Travel: Adventures, destinations, lifestyle")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"   1. Check the data dashboard: http://localhost:3000/actor/data")
    print(f"   2. Look for keyword-relevant content in recent items")
    print(f"   3. Notice improved content quality and relevance")
    print(f"   4. Create tasks with specific keywords to test")
    
    if overall_relevance >= 70:
        print(f"\n🎉 SUCCESS: Keyword relevance significantly improved!")
        print(f"   Content now properly follows keyword instructions")
    else:
        print(f"\n⚠️  NEEDS IMPROVEMENT: Consider further refinement")

if __name__ == '__main__':
    test_keyword_specific_scraping()
