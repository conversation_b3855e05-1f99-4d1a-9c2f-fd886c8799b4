#!/usr/bin/env python3

"""
Test mock authentication functionality
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorAccount

def test_mock_authentication():
    print("🎭 Mock Authentication Test")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # 1. Get account
    print("\n📱 1. Getting Account...")
    account = ActorAccount.objects.filter(user=user).first()
    if not account:
        print("❌ No account found")
        return
    
    print(f"   Account: @{account.platform_username} (ID: {account.id})")
    print(f"   Current last login: {account.last_login}")
    print(f"   Current session valid: {account.is_session_valid()}")
    
    # 2. Test mock authentication
    print(f"\n🚀 2. Testing Mock Authentication...")
    auth_data = {
        'account_id': account.id,
        'mock_mode': True
    }
    
    try:
        response = requests.post(
            f'{base_url}/actor/accounts/authenticate/', 
            headers=headers, 
            json=auth_data,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Mock authentication successful!")
                print(f"   Message: {result.get('message')}")
                print(f"   Mock mode: {result.get('mock_mode')}")
                
                # Check account after mock auth
                account.refresh_from_db()
                print(f"   Updated last login: {account.last_login}")
                print(f"   Updated session valid: {account.is_session_valid()}")
                
                # Check session data
                session_data = account.decrypt_session_data()
                print(f"   Session data exists: {bool(session_data)}")
                if session_data:
                    print(f"   Mock session: {session_data.get('mock_session')}")
                    print(f"   Session type: {session_data.get('session_type')}")
                
            else:
                print(f"❌ Mock authentication failed: {result.get('error')}")
        else:
            print(f"❌ Mock authentication request failed")
            
    except Exception as e:
        print(f"❌ Mock authentication error: {e}")
    
    # 3. Test task execution with mock auth
    print(f"\n🚀 3. Testing Task Execution with Mock Auth...")
    
    # Create a test task
    task = ActorTask.objects.create(
        user=user,
        actor_account=account,
        platform=account.platform,
        task_name='Mock Auth Test Task',
        task_type='CONTENT_SEARCH',
        keywords='mock test',
        max_items=3
    )
    
    try:
        response = requests.post(
            f'{base_url}/actor/tasks/execute/', 
            headers=headers, 
            json={'task_id': task.id},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Task execution successful with mock auth!")
                print(f"   Message: {result.get('message')}")
                print(f"   Items scraped: {result.get('items_scraped', 0)}")
                
                # Check task status
                task.refresh_from_db()
                print(f"   Task status: {task.status}")
                print(f"   Progress: {task.progress_percentage}%")
                
            else:
                print(f"❌ Task execution failed: {result.get('error')}")
        else:
            print(f"❌ Task execution request failed")
            
    except Exception as e:
        print(f"❌ Task execution error: {e}")
    
    # 4. Test account details API
    print(f"\n🔍 4. Testing Account Details API...")
    try:
        response = requests.get(
            f'{base_url}/actor/accounts/{account.id}/details/', 
            headers=headers,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                account_info = result['account']
                print(f"✅ Account details retrieved")
                print(f"   Login Status: {account_info.get('login_status')}")
                print(f"   Session Valid: {account_info.get('session_valid')}")
                print(f"   Last Login: {account_info.get('last_login')}")
                
                # Check platform info
                platform_info = account_info.get('platform_info', {})
                if platform_info:
                    print(f"   Followers: {platform_info.get('follower_count', 'N/A')}")
                    print(f"   Videos: {platform_info.get('video_count', 'N/A')}")
                    print(f"   Account Type: {platform_info.get('account_type', 'N/A')}")
                
            else:
                print(f"❌ Account details failed: {result.get('error')}")
        else:
            print(f"❌ Account details request failed")
            
    except Exception as e:
        print(f"❌ Account details error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Mock Authentication Test Complete!")
    print("\n📋 Summary:")
    print("✅ Mock Authentication: Fast, instant authentication for testing")
    print("✅ Task Execution: Works with mock authentication")
    print("✅ Account Details: Shows updated status and platform info")
    print("✅ Session Management: Properly handles mock session data")
    print("\n💡 Usage:")
    print("• Use 'Quick Test Authentication' for instant testing")
    print("• Use 'Real Platform Authentication' for actual TikTok login")
    print("• Mock auth allows task execution without real platform connection")

if __name__ == '__main__':
    test_mock_authentication()
