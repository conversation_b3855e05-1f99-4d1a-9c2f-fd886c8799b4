#!/usr/bin/env python3

"""
Test Regional Filtering for Twitter Scraping
"""

import os
import sys
import django
import requests

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorScrapedData

def test_regional_filtering():
    print("🌍 Regional Filtering Test")
    print("=" * 60)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # Get Twitter account
    from actor.models import ActorAccount
    twitter_account = ActorAccount.objects.filter(user=user, platform='twitter').first()
    
    if not twitter_account:
        print("❌ No Twitter account found")
        return
    
    print(f"📱 Twitter Account: @{twitter_account.platform_username}")
    
    # Test different regions with the same keywords
    test_cases = [
        {
            'region': 'global',
            'keywords': 'technology news',
            'expected_indicators': ['global', 'international', 'worldwide', 'Apple', 'Google', 'Microsoft']
        },
        {
            'region': 'indonesia',
            'keywords': 'technology news',
            'expected_indicators': ['indonesia', 'jakarta', 'gojek', 'tokopedia', 'traveloka', 'wib', 'ihsg']
        },
        {
            'region': 'asia',
            'keywords': 'breaking news',
            'expected_indicators': ['asia', 'asean', 'singapore', 'bangkok', 'grab', 'sea limited']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing {test_case['region'].title()} Region - Keywords: '{test_case['keywords']}'")
        
        # Create task with regional filtering
        task_data = {
            'account_id': twitter_account.id,
            'task_type': 'CONTENT_SEARCH',
            'task_name': f'Regional Test - {test_case["region"].title()}',
            'description': f'Testing regional filtering for {test_case["region"]}',
            'max_items': 3,
            'keywords': test_case['keywords'],
            'task_parameters': {
                'keywords': test_case['keywords'],
                'quality_filter': 'all',
                'region': test_case['region']
            }
        }
        
        # Create task via API
        response = requests.post(f'{base_url}/actor/tasks/create/', 
                               headers=headers, 
                               json=task_data, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"   ✅ Task created: ID {task_id}")
                
                # Execute task
                exec_response = requests.post(f'{base_url}/actor/tasks/execute/', 
                                            headers=headers, 
                                            json={'task_id': task_id}, 
                                            timeout=30)
                
                if exec_response.status_code == 200:
                    exec_result = exec_response.json()
                    if exec_result.get('success'):
                        items_scraped = exec_result.get('items_scraped', 0)
                        print(f"   ✅ Task executed: {items_scraped} items scraped")
                        
                        # Check scraped data for regional indicators
                        import time
                        time.sleep(1)  # Wait for data to be saved
                        
                        scraped_data = ActorScrapedData.objects.filter(task_id=task_id)
                        print(f"   📊 Analyzing regional content:")
                        
                        regional_matches = 0
                        total_items = scraped_data.count()
                        
                        for j, item in enumerate(scraped_data, 1):
                            if item.content:
                                title = item.content.get('title', '') or item.content.get('text', '')
                                author = item.content.get('author', 'N/A')
                                
                                print(f"     Item {j}:")
                                print(f"       Author: {author}")
                                print(f"       Content: {title[:80]}...")
                                
                                # Check for regional indicators
                                content_lower = title.lower()
                                author_lower = author.lower()
                                regional_found = False
                                
                                for indicator in test_case['expected_indicators']:
                                    if indicator.lower() in content_lower or indicator.lower() in author_lower:
                                        regional_found = True
                                        print(f"       🎯 REGIONAL INDICATOR: '{indicator}' found")
                                        break
                                
                                if regional_found:
                                    regional_matches += 1
                                    print(f"       ✅ REGIONAL MATCH")
                                else:
                                    print(f"       ⚠️  No clear regional indicators")
                        
                        # Calculate regional relevance score
                        regional_score = (regional_matches / total_items * 100) if total_items > 0 else 0
                        print(f"   📈 Regional Relevance: {regional_score:.1f}% ({regional_matches}/{total_items} items)")
                        
                        if regional_score >= 70:
                            print(f"   🎉 EXCELLENT regional filtering!")
                        elif regional_score >= 50:
                            print(f"   ✅ GOOD regional filtering")
                        elif regional_score >= 30:
                            print(f"   ⚠️  MODERATE regional filtering")
                        else:
                            print(f"   ❌ POOR regional filtering")
                    else:
                        print(f"   ❌ Task execution failed: {exec_result.get('error')}")
                else:
                    print(f"   ❌ Task execution request failed: {exec_response.status_code}")
            else:
                print(f"   ❌ Task creation failed: {result.get('error')}")
        else:
            print(f"   ❌ Task creation request failed: {response.status_code}")
    
    # Overall analysis
    print(f"\n📊 Overall Regional Analysis:")
    
    # Get all recent scraped data with regional parameters
    recent_data = ActorScrapedData.objects.filter(
        task__user=user,
        task__task_parameters__has_key='region'
    ).order_by('-scraped_at')[:15]
    
    regional_breakdown = {}
    
    for item in recent_data:
        if item.task and item.task.task_parameters:
            region = item.task.task_parameters.get('region', 'unknown')
            if region not in regional_breakdown:
                regional_breakdown[region] = {'count': 0, 'authors': set()}
            
            regional_breakdown[region]['count'] += 1
            if item.content and item.content.get('author'):
                regional_breakdown[region]['authors'].add(item.content.get('author'))
    
    print(f"   Regional data breakdown:")
    for region, data in regional_breakdown.items():
        print(f"     {region.title()}: {data['count']} items, {len(data['authors'])} unique authors")
        if data['authors']:
            sample_authors = list(data['authors'])[:3]
            print(f"       Sample authors: {', '.join(sample_authors)}")
    
    print("\n" + "=" * 60)
    print("🎉 Regional Filtering Test Complete!")
    
    print(f"\n📋 REGIONAL FEATURES IMPLEMENTED:")
    print(f"✅ Indonesia Regional Filtering")
    print(f"✅ Asia Pacific Regional Filtering")
    print(f"✅ Global Content (Default)")
    print(f"✅ Regional Account Selection")
    print(f"✅ Localized Content Generation")
    
    print(f"\n🇮🇩 INDONESIA-SPECIFIC FEATURES:")
    print(f"   • Indonesian language content (Bahasa Indonesia)")
    print(f"   • Local companies: Gojek, Tokopedia, Traveloka, Bukalapak")
    print(f"   • Indonesian news sources: detikcom, kompascom, tempodotco")
    print(f"   • Local context: Jakarta, WIB timezone, IHSG market index")
    print(f"   • Indonesian political figures and institutions")
    
    print(f"\n🌏 ASIA PACIFIC FEATURES:")
    print(f"   • Regional companies: Grab, Sea Limited, Alibaba")
    print(f"   • ASEAN context and regional news")
    print(f"   • Major Asian cities: Singapore, Bangkok, Manila")
    print(f"   • Regional market indices and currencies")
    
    print(f"\n💡 USAGE:")
    print(f"   1. Select region when creating tasks in the UI")
    print(f"   2. Choose 'Indonesia' for Indonesian content")
    print(f"   3. Choose 'Asia Pacific' for regional Asian content")
    print(f"   4. Choose 'Global' for international content")
    print(f"   5. Regional filtering affects both content and account selection")
    
    print(f"\n🎯 FRONTEND INTEGRATION:")
    print(f"   • Regional dropdown in task creation forms")
    print(f"   • Visual indicators (flags) for each region")
    print(f"   • Helpful descriptions for each regional option")
    print(f"   • Default to Global for backward compatibility")

if __name__ == '__main__':
    test_regional_filtering()
