#!/usr/bin/env python3

"""
Debug authentication issues
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorAccount
from actor.services.actor_service import ActorService

def debug_authentication():
    print("🔐 Authentication Debug")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # 1. Get account from database
    print("\n📱 1. Getting Account from Database...")
    account = ActorAccount.objects.filter(user=user).first()
    if not account:
        print("❌ No account found")
        return
    
    print(f"   Account: @{account.platform_username} (ID: {account.id})")
    print(f"   Platform: {account.platform}")
    print(f"   Last login: {account.last_login}")
    print(f"   Session expires: {account.session_expires_at}")
    print(f"   Login attempts: {account.login_attempts}")
    print(f"   Is blocked: {account.is_blocked}")
    
    # Check session data
    try:
        session_data = account.decrypt_session_data()
        print(f"   Session data exists: {bool(session_data)}")
        if session_data:
            print(f"   Session keys: {list(session_data.keys())}")
    except Exception as e:
        print(f"   Session data error: {e}")
    
    # 2. Test direct service authentication
    print(f"\n🔧 2. Testing Direct Service Authentication...")
    actor_service = ActorService()
    
    try:
        result = actor_service.authenticate_account(account.id)
        print(f"   Service result: {result}")
        
        if result.get('success'):
            print("✅ Service authentication successful")
            
            # Check account after service auth
            account.refresh_from_db()
            print(f"   Updated last login: {account.last_login}")
            print(f"   Updated session expires: {account.session_expires_at}")
            print(f"   Updated login attempts: {account.login_attempts}")
            
            try:
                session_data = account.decrypt_session_data()
                print(f"   Updated session data exists: {bool(session_data)}")
            except Exception as e:
                print(f"   Updated session data error: {e}")
        else:
            print(f"❌ Service authentication failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Service authentication error: {e}")
    
    # 3. Test API authentication
    print(f"\n🌐 3. Testing API Authentication...")
    auth_data = {
        'account_id': account.id
    }
    
    try:
        response = requests.post(
            f'{base_url}/actor/accounts/authenticate/', 
            headers=headers, 
            json=auth_data,
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ API authentication successful")
            else:
                print(f"❌ API authentication failed: {result.get('error')}")
        else:
            print(f"❌ API request failed")
            
    except Exception as e:
        print(f"❌ API authentication error: {e}")
    
    # 4. Check account details after API auth
    print(f"\n📊 4. Checking Account Details After API Auth...")
    account.refresh_from_db()
    print(f"   Final last login: {account.last_login}")
    print(f"   Final session expires: {account.session_expires_at}")
    print(f"   Final login attempts: {account.login_attempts}")
    
    try:
        session_data = account.decrypt_session_data()
        print(f"   Final session data exists: {bool(session_data)}")
        if session_data:
            print(f"   Final session keys: {list(session_data.keys())}")
    except Exception as e:
        print(f"   Final session data error: {e}")
    
    # 5. Test account details API
    print(f"\n🔍 5. Testing Account Details API...")
    try:
        response = requests.get(
            f'{base_url}/actor/accounts/{account.id}/details/', 
            headers=headers,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                account_info = result['account']
                print(f"✅ Account details retrieved")
                print(f"   API Login Status: {account_info.get('login_status')}")
                print(f"   API Session Valid: {account_info.get('session_valid')}")
                print(f"   API Last Login: {account_info.get('last_login')}")
                print(f"   API Session Expires: {account_info.get('session_expires_at')}")
                print(f"   API Login Attempts: {account_info.get('login_attempts')}")
            else:
                print(f"❌ Account details failed: {result.get('error')}")
        else:
            print(f"❌ Account details request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Account details error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Authentication Debug Complete!")

if __name__ == '__main__':
    debug_authentication()
