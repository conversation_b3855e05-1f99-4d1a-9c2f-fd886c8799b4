#!/usr/bin/env python3

"""
Quick test for account details endpoint
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def test_account_details():
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    # Test account details endpoint
    url = 'http://localhost:8000/api/actor/accounts/4/details/'
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    print("🔍 Testing Account Details Endpoint")
    print(f"🔗 URL: {url}")
    print(f"🔑 Token: {access_token[:20]}...")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"📊 Status: {response.status_code}")
        print(f"📄 Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Account details retrieved successfully!")
        else:
            print("❌ Failed to get account details")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    test_account_details()
