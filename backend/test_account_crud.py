#!/usr/bin/env python
"""
Test script to verify Account CRUD operations work correctly.
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount
from actor.services.actor_service import ActorService

def test_account_crud():
    print("🧪 Account CRUD Operations Test")
    print("=" * 50)
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='test_crud_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'CRUD'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    print(f"👤 Test User: {user.username}")
    
    # Initialize service
    actor_service = ActorService()
    
    # Clean up any existing test accounts
    ActorAccount.objects.filter(
        user=user,
        platform='tiktok',
        platform_username='test_crud_account'
    ).delete()
    
    print("🧹 Cleaned up existing test accounts")
    
    # Test 1: CREATE
    print("\n1️⃣ Testing CREATE operation...")
    try:
        create_result = actor_service.create_account(
            user=user,
            platform='tiktok',
            username='test_crud_account',
            password='test_password_123',
            email='<EMAIL>'
        )
        
        if create_result['success']:
            account_id = create_result['account_id']
            print(f"   ✅ CREATE successful: Account ID {account_id}")
        else:
            print(f"   ❌ CREATE failed: {create_result['error']}")
            return False
            
    except Exception as e:
        print(f"   ❌ CREATE exception: {str(e)}")
        return False
    
    # Test 2: READ
    print("\n2️⃣ Testing READ operation...")
    try:
        accounts = actor_service.get_user_accounts(user=user, platform='tiktok')
        
        if accounts and len(accounts) > 0:
            account = accounts[0]
            print(f"   ✅ READ successful: Found account @{account['username']}")
            print(f"   Platform: {account['platform']}")
            print(f"   Email: {account['email']}")
            print(f"   Active: {account['is_active']}")
        else:
            print("   ❌ READ failed: No accounts found")
            return False
            
    except Exception as e:
        print(f"   ❌ READ exception: {str(e)}")
        return False
    
    # Test 3: UPDATE
    print("\n3️⃣ Testing UPDATE operation...")
    try:
        update_result = actor_service.update_account(
            user=user,
            account_id=account_id,
            update_data={
                'username': 'test_crud_account_updated',
                'email': '<EMAIL>'
            }
        )
        
        if update_result['success']:
            print(f"   ✅ UPDATE successful")
            print(f"   Updated username: {update_result['account']['username']}")
            print(f"   Updated email: {update_result['account']['email']}")
        else:
            print(f"   ❌ UPDATE failed: {update_result['error']}")
            return False
            
    except Exception as e:
        print(f"   ❌ UPDATE exception: {str(e)}")
        return False
    
    # Test 4: READ after UPDATE
    print("\n4️⃣ Testing READ after UPDATE...")
    try:
        account_details = actor_service.get_account_details(user=user, account_id=account_id)
        
        if account_details['success']:
            account = account_details['account']
            print(f"   ✅ READ after UPDATE successful")
            print(f"   Username: {account['username']}")
            print(f"   Email: {account['email']}")
        else:
            print(f"   ❌ READ after UPDATE failed: {account_details['error']}")
            return False
            
    except Exception as e:
        print(f"   ❌ READ after UPDATE exception: {str(e)}")
        return False
    
    # Test 5: DELETE
    print("\n5️⃣ Testing DELETE operation...")
    try:
        delete_result = actor_service.delete_account(
            user=user,
            account_id=account_id
        )
        
        if delete_result['success']:
            print(f"   ✅ DELETE successful: {delete_result['message']}")
        else:
            print(f"   ❌ DELETE failed: {delete_result['error']}")
            return False
            
    except Exception as e:
        print(f"   ❌ DELETE exception: {str(e)}")
        return False
    
    # Test 6: READ after DELETE
    print("\n6️⃣ Testing READ after DELETE...")
    try:
        account_details = actor_service.get_account_details(user=user, account_id=account_id)
        
        if not account_details['success'] and 'not found' in account_details['error'].lower():
            print(f"   ✅ DELETE verification successful: Account not found (deleted)")
        else:
            print(f"   ❌ DELETE verification failed: Account still exists")
            return False
            
    except Exception as e:
        print(f"   ❌ DELETE verification exception: {str(e)}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Account CRUD Test Complete!")
    print("\n📋 ACCOUNT CRUD OPERATIONS TESTED:")
    print("✅ CREATE - Create new actor accounts")
    print("✅ READ - Retrieve account details and lists")
    print("✅ UPDATE - Modify existing account information")
    print("✅ DELETE - Remove accounts with safety checks")
    print("\n🎯 API ENDPOINTS:")
    print("   POST   /api/actor/accounts/create/")
    print("   GET    /api/actor/accounts/list/")
    print("   GET    /api/actor/accounts/<id>/")
    print("   PUT    /api/actor/accounts/<id>/")
    print("   DELETE /api/actor/accounts/<id>/delete/")
    
    return True

if __name__ == '__main__':
    success = test_account_crud()
    if success:
        print("\n🏆 All Account CRUD operations working correctly!")
    else:
        print("\n❌ Some Account CRUD operations failed!")
        sys.exit(1)