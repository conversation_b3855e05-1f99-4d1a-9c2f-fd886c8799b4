#!/usr/bin/env python3

"""
Debug task creation and listing issues
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorAccount

def debug_task_system():
    print("🔍 Task System Debug")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username} (ID: {user.id})")
    print(f"🔑 Token: {access_token[:20]}...")
    
    # 1. Check existing tasks in database
    print("\n📊 1. Checking Database Tasks...")
    db_tasks = ActorTask.objects.filter(user=user)
    print(f"   Database tasks count: {db_tasks.count()}")
    for task in db_tasks:
        print(f"   - {task.task_name} ({task.status}) - Created: {task.created_at}")
    
    # 2. Check existing accounts
    print("\n📱 2. Checking Accounts...")
    accounts = ActorAccount.objects.filter(user=user)
    print(f"   Database accounts count: {accounts.count()}")
    for account in accounts:
        print(f"   - @{account.platform_username} ({account.platform}) - ID: {account.id}")
    
    if not accounts.exists():
        print("❌ No accounts found! Please create an account first.")
        return
    
    account = accounts.first()
    
    # 3. Test API task creation
    print(f"\n📋 3. Testing Task Creation via API...")
    task_data = {
        'account_id': account.id,
        'task_type': 'CONTENT_SEARCH',
        'task_name': 'Debug Test Task',
        'description': 'Testing task creation and listing',
        'max_items': 10,
        'keywords': 'debug test',
        'task_parameters': {
            'keywords': 'debug test',
            'quality_filter': 'all',
            'include_metadata': True
        }
    }
    
    print(f"   Task data: {json.dumps(task_data, indent=2)}")
    
    try:
        response = requests.post(
            f'{base_url}/actor/tasks/create/', 
            headers=headers, 
            json=task_data,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ Task created successfully! ID: {task_id}")
            else:
                print(f"❌ Task creation failed: {result.get('error')}")
        else:
            print(f"❌ API request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # 4. Check database again after creation
    print("\n📊 4. Checking Database After Creation...")
    db_tasks_after = ActorTask.objects.filter(user=user)
    print(f"   Database tasks count: {db_tasks_after.count()}")
    for task in db_tasks_after:
        print(f"   - {task.task_name} ({task.status}) - Created: {task.created_at}")
    
    # 5. Test API task listing
    print(f"\n📋 5. Testing Task Listing via API...")
    try:
        response = requests.get(
            f'{base_url}/actor/tasks/list/', 
            headers=headers,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                tasks = result.get('tasks', [])
                print(f"✅ API returned {len(tasks)} tasks")
                for task in tasks:
                    print(f"   - {task.get('name')} ({task.get('status')})")
            else:
                print(f"❌ API listing failed: {result.get('error')}")
        else:
            print(f"❌ API request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Debug Complete!")

if __name__ == '__main__':
    debug_task_system()
