#!/usr/bin/env python3

"""
Test Twitter account creation
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorAccount

def test_twitter_account_creation():
    print("🐦 Twitter Account Creation Test")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # Check existing Twitter accounts
    existing_twitter = ActorAccount.objects.filter(user=user, platform='twitter')
    print(f"📊 Existing Twitter accounts: {existing_twitter.count()}")
    
    # Test account creation
    account_data = {
        'platform': 'twitter',
        'username': 'test_twitter_user',
        'password': 'test_password_123',
        'email': '<EMAIL>'
    }
    
    print(f"\n📱 Testing Twitter account creation...")
    print(f"   Platform: {account_data['platform']}")
    print(f"   Username: {account_data['username']}")
    
    response = requests.post(f'{base_url}/actor/accounts/create/', headers=headers, json=account_data, timeout=10)
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            account_id = result.get('account_id')
            print(f"✅ Twitter account created successfully: ID {account_id}")
            
            # Verify account in database
            twitter_account = ActorAccount.objects.get(id=account_id)
            print(f"   Database verification:")
            print(f"     - Platform: {twitter_account.platform}")
            print(f"     - Username: {twitter_account.platform_username}")
            print(f"     - User: {twitter_account.user.username}")
            print(f"     - Created: {twitter_account.created_at}")
        else:
            print(f"❌ Account creation failed: {result.get('error')}")
    else:
        print(f"❌ Account creation request failed")
        try:
            error_data = response.json()
            print(f"   Error details: {error_data}")
        except:
            print(f"   Raw response: {response.text}")

if __name__ == '__main__':
    test_twitter_account_creation()
