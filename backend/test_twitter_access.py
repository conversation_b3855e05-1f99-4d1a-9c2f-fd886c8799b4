#!/usr/bin/env python3

"""
Test Twitter Access
Simple test to see what we can access from Twitter/X without authentication.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import Chrome<PERSON>river<PERSON>anager

def test_twitter_access():
    print("🔍 Testing Twitter/X Access")
    print("=" * 40)
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    
    # Initialize driver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Test 1: Access Twitter homepage
        print("\n1️⃣ Testing Twitter homepage access...")
        driver.get("https://twitter.com")
        time.sleep(3)
        
        print(f"   URL: {driver.current_url}")
        print(f"   Title: {driver.title}")
        
        # Check if redirected to X.com
        if 'x.com' in driver.current_url:
            print("   ✅ Redirected to X.com (expected)")
        
        # Test 2: Try to access a public profile
        print("\n2️⃣ Testing public profile access...")
        driver.get("https://twitter.com/twitter")
        time.sleep(3)
        
        print(f"   URL: {driver.current_url}")
        print(f"   Title: {driver.title}")
        
        # Check page content
        page_source = driver.page_source
        
        if 'login' in page_source.lower() or 'sign in' in page_source.lower():
            print("   ⚠️  Login required for profile access")
        
        if 'rate limit' in page_source.lower():
            print("   ⚠️  Rate limited")
        
        # Test 3: Try search without login
        print("\n3️⃣ Testing search access...")
        search_url = "https://twitter.com/search?q=test&src=typed_query"
        driver.get(search_url)
        time.sleep(3)
        
        print(f"   URL: {driver.current_url}")
        print(f"   Title: {driver.title}")
        
        # Look for any content
        articles = driver.find_elements(By.TAG_NAME, "article")
        divs_with_testid = driver.find_elements(By.CSS_SELECTOR, "div[data-testid]")
        spans_with_text = driver.find_elements(By.CSS_SELECTOR, "span")
        
        print(f"   Found {len(articles)} article elements")
        print(f"   Found {len(divs_with_testid)} divs with data-testid")
        print(f"   Found {len(spans_with_text)} span elements")
        
        # Check for specific Twitter elements
        tweet_elements = driver.find_elements(By.CSS_SELECTOR, '[data-testid="tweet"]')
        tweettext_elements = driver.find_elements(By.CSS_SELECTOR, '[data-testid="tweetText"]')
        
        print(f"   Found {len(tweet_elements)} tweet elements")
        print(f"   Found {len(tweettext_elements)} tweetText elements")
        
        # Test 4: Try alternative Twitter frontends
        print("\n4️⃣ Testing alternative Twitter frontends...")
        
        nitter_instances = [
            'https://nitter.net/search?q=test',
            'https://nitter.it/search?q=test',
        ]
        
        for nitter_url in nitter_instances:
            try:
                print(f"   Testing: {nitter_url}")
                driver.get(nitter_url)
                time.sleep(2)
                
                if driver.current_url != nitter_url:
                    print(f"     Redirected to: {driver.current_url}")
                
                # Look for tweet-like content
                tweet_divs = driver.find_elements(By.CSS_SELECTOR, ".tweet")
                tweet_content = driver.find_elements(By.CSS_SELECTOR, ".tweet-content")
                
                print(f"     Found {len(tweet_divs)} tweet divs")
                print(f"     Found {len(tweet_content)} tweet content elements")
                
                if tweet_content:
                    sample_text = tweet_content[0].text[:50] if tweet_content[0].text else "No text"
                    print(f"     Sample content: {sample_text}...")
                    print(f"   ✅ {nitter_url.split('//')[1].split('/')[0]} is accessible!")
                    break
                    
            except Exception as e:
                print(f"     ❌ Failed: {str(e)}")
        
        # Test 5: Check what we can actually scrape
        print("\n5️⃣ Analyzing scrapeable content...")
        
        # Go back to Twitter search
        driver.get("https://twitter.com/search?q=python&src=typed_query")
        time.sleep(5)
        
        # Try to find any text content
        all_text_elements = driver.find_elements(By.XPATH, "//*[text()]")
        text_contents = []
        
        for element in all_text_elements[:20]:  # Check first 20 elements
            text = element.text.strip()
            if text and len(text) > 10 and len(text) < 200:
                text_contents.append(text)
        
        print(f"   Found {len(text_contents)} text elements")
        
        if text_contents:
            print("   Sample text content:")
            for i, text in enumerate(text_contents[:3]):
                print(f"     {i+1}. {text[:60]}...")
        
        # Check for login prompts
        login_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Log in') or contains(text(), 'Sign in') or contains(text(), 'Sign up')]")
        if login_elements:
            print(f"   ⚠️  Found {len(login_elements)} login-related elements")
            print("   Twitter likely requires authentication for content access")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        driver.quit()
        print("\n✅ WebDriver closed")
    
    print("\n" + "=" * 40)
    print("🎯 Twitter Access Test Summary:")
    
    print(f"\n📊 FINDINGS:")
    print(f"   • Twitter/X has strong anti-bot measures")
    print(f"   • Most content requires authentication")
    print(f"   • Public search may be limited or blocked")
    print(f"   • Alternative frontends (Nitter) may work better")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"   1. Use Nitter instances for public Twitter data")
    print(f"   2. Implement Twitter API authentication")
    print(f"   3. Use alternative social media sources")
    print(f"   4. Focus on publicly available RSS feeds")
    
    print(f"\n🔧 NEXT STEPS:")
    print(f"   • Modify scraper to use Nitter instances")
    print(f"   • Add Twitter API integration")
    print(f"   • Implement fallback to other social sources")
    print(f"   • Add user authentication for Twitter access")

if __name__ == '__main__':
    test_twitter_access()
