#!/usr/bin/env python3

"""
Test Twitter authentication fix
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from django.utils import timezone
from datetime import timedelta

def test_twitter_auth_fix():
    print("🔐 Twitter Authentication Fix Test")
    print("=" * 50)
    
    user = User.objects.get(username='test_actor_user')
    service = ActorService()
    
    # Get Twitter account
    twitter_account = ActorAccount.objects.filter(user=user, platform='twitter').first()
    
    if not twitter_account:
        print("❌ No Twitter account found")
        return
    
    print(f"📱 Twitter Account: @{twitter_account.platform_username}")
    print(f"   Account ID: {twitter_account.id}")
    
    # Make session invalid for testing
    print(f"\n🔧 Making session invalid for testing...")
    twitter_account.session_expires_at = timezone.now() - timedelta(hours=1)
    twitter_account.save()
    print(f"   Session valid before auth: {twitter_account.is_session_valid()}")
    print(f"   Session expires at: {twitter_account.session_expires_at}")
    
    # Test authentication
    print(f"\n🚀 Testing authentication...")
    try:
        result = service.authenticate_account(twitter_account.id)
        print(f"   Authentication success: {result.get('success')}")
        
        if result.get('success'):
            print(f"   Message: {result.get('message')}")
            
            # Check session after authentication
            twitter_account.refresh_from_db()
            print(f"\n✅ Session status after authentication:")
            print(f"   Session valid: {twitter_account.is_session_valid()}")
            print(f"   Session expires at: {twitter_account.session_expires_at}")
            print(f"   Last login: {twitter_account.last_login}")
            
            # Check session data
            session_data = twitter_account.decrypt_session_data()
            if session_data:
                print(f"   Session data keys: {list(session_data.keys())}")
                print(f"   Authenticated: {session_data.get('authenticated')}")
                print(f"   Username: {session_data.get('username')}")
                print(f"   Auth token: {session_data.get('auth_token', 'N/A')[:20]}...")
            else:
                print(f"   ❌ No session data found")
        else:
            print(f"   ❌ Authentication failed: {result.get('error')}")
    
    except Exception as e:
        print(f"   ❌ Authentication error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎉 Twitter Authentication Fix Test Complete!")

if __name__ == '__main__':
    test_twitter_auth_fix()
