#!/usr/bin/env python3

"""
Test Real Twitter Scraping Implementation
"""

import os
import sys
import django
import requests

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def test_real_twitter_scraping():
    print("🔍 Testing Real Twitter Scraping Implementation")
    print("=" * 70)
    
    # Test direct scraper first
    print("1️⃣ Testing Direct Twitter Scraper:")
    
    try:
        from actor.scrapers.twitter_scraper import TwitterScraper
        
        scraper = TwitterScraper()
        
        # Test different keywords
        test_queries = [
            'prabowo',
            'artificial intelligence',
            'indonesia news',
            'technology',
            'football'
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   {i}. Testing query: '{query}'")
            
            result = scraper.search_tweets(query, count=3)
            
            if result.get('success'):
                tweets = result.get('tweets', [])
                source = result.get('source', 'unknown')
                
                print(f"      ✅ Success: {len(tweets)} tweets found")
                print(f"      📊 Source: {source}")
                
                # Check if it's real scraped data
                if source == 'real_twitter_scraper':
                    print(f"      🎉 REAL TWITTER DATA SCRAPED!")
                elif source == 'fallback_realistic_data':
                    print(f"      ⚠️  Using fallback data (real scraping failed)")
                else:
                    print(f"      ❓ Unknown source: {source}")
                
                # Show sample tweets
                for j, tweet in enumerate(tweets[:2], 1):
                    text = tweet.get('full_text', tweet.get('text', 'No text'))
                    author = tweet.get('user', {}).get('screen_name', 'unknown')
                    is_real = tweet.get('real_scraped', False)
                    scrape_source = tweet.get('scrape_source', 'unknown')
                    
                    print(f"        Tweet {j}: @{author}")
                    print(f"          Text: {text[:60]}...")
                    print(f"          Real Scraped: {'✅ YES' if is_real else '❌ NO'}")
                    print(f"          Scrape Source: {scrape_source}")
            else:
                error = result.get('error', 'Unknown error')
                print(f"      ❌ Failed: {error}")
        
        scraper.close()
        
    except Exception as e:
        print(f"   ❌ Direct scraper test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test via API
    print(f"\n2️⃣ Testing Real Scraping via API:")
    
    try:
        # Get test user and token
        user = User.objects.get(username='test_actor_user')
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        headers = {
            'Authorization': f'JWT {access_token}',
            'Content-Type': 'application/json'
        }
        
        base_url = 'http://localhost:8000/api'
        
        # Get Twitter account
        from actor.models import ActorAccount
        twitter_account = ActorAccount.objects.filter(user=user, platform='twitter').first()
        
        if not twitter_account:
            print("   ❌ No Twitter account found")
            return
        
        print(f"   📱 Twitter Account: @{twitter_account.platform_username}")
        
        # Test real scraping with specific keywords
        test_cases = [
            {
                'keywords': 'prabowo indonesia',
                'region': 'indonesia',
                'description': 'Indonesian political keyword'
            },
            {
                'keywords': 'artificial intelligence AI',
                'region': 'global',
                'description': 'Technology keyword'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   {i}. Testing: {test_case['description']}")
            print(f"      Keywords: '{test_case['keywords']}'")
            print(f"      Region: {test_case['region']}")
            
            # Create task
            task_data = {
                'account_id': twitter_account.id,
                'task_type': 'CONTENT_SEARCH',
                'task_name': f'Real Scraping Test - {test_case["keywords"]}',
                'description': test_case['description'],
                'max_items': 3,
                'keywords': test_case['keywords'],
                'task_parameters': {
                    'keywords': test_case['keywords'],
                    'quality_filter': 'all',
                    'region': test_case['region']
                }
            }
            
            # Create and execute task
            response = requests.post(f'{base_url}/actor/tasks/create/', 
                                   headers=headers, 
                                   json=task_data, 
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result.get('task_id')
                    
                    # Execute task
                    exec_response = requests.post(f'{base_url}/actor/tasks/execute/', 
                                                headers=headers, 
                                                json={'task_id': task_id}, 
                                                timeout=30)
                    
                    if exec_response.status_code == 200:
                        exec_result = exec_response.json()
                        if exec_result.get('success'):
                            items_scraped = exec_result.get('items_scraped', 0)
                            print(f"      ✅ Task executed: {items_scraped} items scraped")
                            
                            # Check scraped data
                            import time
                            time.sleep(1)
                            
                            from actor.models import ActorScrapedData
                            scraped_data = ActorScrapedData.objects.filter(task_id=task_id)
                            
                            print(f"      📊 Analyzing scraped data:")
                            
                            real_scraped_count = 0
                            fallback_count = 0
                            
                            for item in scraped_data:
                                if item.content:
                                    title = item.content.get('title', '') or item.content.get('text', '')
                                    author = item.content.get('author', 'N/A')
                                    source = item.content.get('source', 'unknown')
                                    is_real = item.content.get('real_scraped', False)
                                    
                                    if is_real:
                                        real_scraped_count += 1
                                        print(f"        ✅ REAL: @{author} - {title[:40]}...")
                                    else:
                                        fallback_count += 1
                                        print(f"        ⚠️  FALLBACK: @{author} - {title[:40]}...")
                            
                            print(f"      📈 Results Summary:")
                            print(f"        Real Scraped: {real_scraped_count}/{scraped_data.count()}")
                            print(f"        Fallback Data: {fallback_count}/{scraped_data.count()}")
                            
                            if real_scraped_count > 0:
                                print(f"      🎉 SUCCESS: Real Twitter data was scraped!")
                            else:
                                print(f"      ⚠️  Using fallback data (real scraping may have failed)")
                        else:
                            print(f"      ❌ Task execution failed: {exec_result.get('error')}")
                    else:
                        print(f"      ❌ Task execution failed: {exec_response.status_code}")
                else:
                    print(f"      ❌ Task creation failed: {result.get('error')}")
            else:
                print(f"      ❌ Task creation failed: {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ API test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎯 Real Twitter Scraping Test Summary:")
    
    print(f"\n🔧 IMPLEMENTATION STATUS:")
    print(f"✅ Real Twitter scraping methods implemented")
    print(f"✅ Multiple scraping approaches (Twitter, Mobile, Nitter)")
    print(f"✅ HTML parsing for tweet extraction")
    print(f"✅ Fallback to realistic data if real scraping fails")
    print(f"✅ Real scraped data flagging system")
    
    print(f"\n📊 SCRAPING METHODS:")
    print(f"   1. Twitter Public Search - Direct HTML scraping")
    print(f"   2. Mobile Twitter - Mobile site scraping")
    print(f"   3. Nitter Instances - Alternative frontend scraping")
    print(f"   4. Fallback - Realistic mock data if all fail")
    
    print(f"\n⚠️  IMPORTANT NOTES:")
    print(f"   • Twitter/X heavily uses JavaScript, limiting HTML scraping")
    print(f"   • Real scraping may be blocked by anti-bot measures")
    print(f"   • Nitter instances may be unreliable or blocked")
    print(f"   • Fallback data ensures system always works")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   • Monitor real scraping success rates")
    print(f"   • Add more Nitter instances if needed")
    print(f"   • Consider Twitter API integration for production")
    print(f"   • Implement proxy rotation for better success rates")
    
    print(f"\n💡 RESULT:")
    print(f"   The system now attempts REAL Twitter scraping first,")
    print(f"   then falls back to realistic data if needed.")
    print(f"   Users get actual tweets when possible! 🎉")

if __name__ == '__main__':
    test_real_twitter_scraping()
