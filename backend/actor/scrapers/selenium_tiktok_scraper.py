"""Selenium-based TikTok Scraper for Actor System

This module provides real TikTok scraping capabilities using Selenium WebDriver
integrated with the existing crawler_tiktok services.
"""

import logging
import time
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Import existing TikTok crawler services
from crawler_tiktok.services.driver_setup import create_driver, return_driver
from crawler_tiktok.services.user_crawler import scrape_tiktok_user
from crawler_tiktok.services.hashtag_crawler import scrape_tiktok_hashtag
from crawler_tiktok.services.search_crawler import scrape_tiktok_search
from crawler_tiktok.utils.anti_detection import get_random_user_agent, random_delay
from crawler_tiktok.utils.config import LOAD_TIMEOUT, SCROLL_PAUSE_TIME

logger = logging.getLogger(__name__)

class SeleniumTikTokScraper:
    """
    Selenium-based TikTok scraper that uses real WebDriver for scraping.
    
    This scraper integrates with the existing crawler_tiktok services
    to provide real TikTok data extraction capabilities.
    """
    
    def __init__(self, proxy_config: Optional[Dict] = None, account_session: Optional[Dict] = None):
        self.logger = logger
        self.proxy_config = proxy_config
        self.account_session = account_session
        self.driver = None
        self.user_agent = get_random_user_agent()
        self.is_authenticated = False
        
    def _get_proxy_string(self) -> Optional[str]:
        """Convert proxy config to proxy string for Selenium."""
        if not self.proxy_config or not self.proxy_config.get('enabled'):
            return None
            
        proxies = self.proxy_config.get('proxies', [])
        if not proxies:
            return None
            
        # Use first available proxy
        proxy = proxies[0]
        protocol = proxy.get('protocol', 'http')
        host = proxy.get('host')
        port = proxy.get('port')
        
        if host and port:
            return f"{protocol}://{host}:{port}"
        return None
    
    def _setup_authenticated_session(self) -> bool:
        """Setup authenticated session using account session data."""
        if not self.account_session:
            self.logger.info("No account session data available, proceeding without authentication")
            return False
            
        try:
            # Check if session data contains authentication info
            if not self.account_session.get('authenticated'):
                self.logger.warning("Account session data indicates not authenticated")
                return False
                
            # Extract session cookies or tokens if available
            cookies = self.account_session.get('cookies', [])
            tokens = self.account_session.get('tokens', {})
            
            if cookies or tokens:
                self.logger.info("Account session data found, will use for authenticated scraping")
                self.is_authenticated = True
                return True
            else:
                self.logger.warning("Account session data exists but no cookies/tokens found")
                return False
                
        except Exception as e:
            self.logger.error(f"Error setting up authenticated session: {str(e)}")
            return False
    
    def _setup_driver(self) -> bool:
        """Setup Selenium WebDriver for scraping."""
        try:
            proxy_string = self._get_proxy_string()
            self.driver = create_driver(
                headless=True,
                proxy=proxy_string,
                user_agent=self.user_agent
            )
            self.logger.info("Selenium WebDriver initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def search_videos(self, query: str, count: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Search for TikTok videos using Selenium WebDriver.
        
        Args:
            query: Search query
            count: Number of videos to scrape
            **kwargs: Additional parameters
            
        Returns:
            Dict containing scraped videos and metadata
        """
        try:
            self.logger.info(f"Searching TikTok for: {query} using Selenium (authenticated: {self.is_authenticated})")
            
            # Setup authenticated session if available
            self._setup_authenticated_session()
            
            # Use existing search crawler
            proxy_string = self._get_proxy_string()
            result = scrape_tiktok_search(
                keyword=query,
                proxy=proxy_string,
                max_results=count,
                session_data=self.account_session if self.is_authenticated else None
            )
            
            if result and result.get('success'):
                videos = result.get('videos', [])
                
                # Convert to expected format
                formatted_videos = self._format_search_results(videos, query)
                
                return {
                    'success': True,
                    'videos': formatted_videos,
                    'query': query,
                    'count': len(formatted_videos),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'selenium_scraper',
                    'scraping_method': 'real_selenium'
                }
            else:
                error_msg = result.get('error', 'Search failed') if result else 'No results'
                return {
                    'success': False,
                    'error': error_msg,
                    'query': query
                }
                
        except Exception as e:
            self.logger.error(f"TikTok search failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
    
    def get_user_videos(self, username: str, count: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Get videos from a specific TikTok user using Selenium WebDriver.
        
        Args:
            username: TikTok username
            count: Number of videos to scrape
            **kwargs: Additional parameters
            
        Returns:
            Dict containing scraped videos and metadata
        """
        try:
            self.logger.info(f"Getting videos from @{username} using Selenium (authenticated: {self.is_authenticated})")
            
            # Setup authenticated session if available
            self._setup_authenticated_session()
            
            # Use existing user crawler
            proxy_string = self._get_proxy_string()
            result = scrape_tiktok_user(
                username=username,
                proxy=proxy_string,
                max_videos=count,
                session_data=self.account_session if self.is_authenticated else None
            )
            
            if result and result.get('success'):
                videos = result.get('videos', [])
                profile_data = result.get('profile', {})
                
                # Convert to expected format
                formatted_videos = self._format_user_videos(videos, username, profile_data)
                
                return {
                    'success': True,
                    'videos': formatted_videos,
                    'username': username,
                    'profile': profile_data,
                    'count': len(formatted_videos),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'selenium_scraper',
                    'scraping_method': 'real_selenium'
                }
            else:
                error_msg = result.get('error', 'User scraping failed') if result else 'No results'
                return {
                    'success': False,
                    'error': error_msg,
                    'username': username
                }
                
        except Exception as e:
            self.logger.error(f"User videos scraping failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'username': username
            }
    
    def scrape_hashtag(self, hashtag: str, count: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Scrape videos from a TikTok hashtag using Selenium WebDriver.
        
        Args:
            hashtag: Hashtag to scrape
            count: Number of videos to scrape
            **kwargs: Additional parameters
            
        Returns:
            Dict containing scraped videos and metadata
        """
        try:
            self.logger.info(f"Scraping hashtag #{hashtag} using Selenium (authenticated: {self.is_authenticated})")
            
            # Setup authenticated session if available
            self._setup_authenticated_session()
            
            # Use existing hashtag crawler
            proxy_string = self._get_proxy_string()
            result = scrape_tiktok_hashtag(
                hashtag=hashtag,
                proxy=proxy_string,
                max_videos=count,
                session_data=self.account_session if self.is_authenticated else None
            )
            
            if result and result.get('success'):
                videos = result.get('videos', [])
                
                # Convert to expected format
                formatted_videos = self._format_hashtag_videos(videos, hashtag)
                
                return {
                    'success': True,
                    'videos': formatted_videos,
                    'hashtag': hashtag,
                    'count': len(formatted_videos),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'selenium_scraper',
                    'scraping_method': 'real_selenium'
                }
            else:
                error_msg = result.get('error', 'Hashtag scraping failed') if result else 'No results'
                return {
                    'success': False,
                    'error': error_msg,
                    'hashtag': hashtag
                }
                
        except Exception as e:
            self.logger.error(f"Hashtag scraping failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'hashtag': hashtag
            }
    
    def _format_search_results(self, videos: List[Dict], query: str) -> List[Dict[str, Any]]:
        """Format search results to match expected actor data format."""
        formatted_videos = []
        
        for video in videos:
            formatted_video = self._format_video_data(video)
            formatted_video['search_query'] = query
            formatted_video['scrape_type'] = 'search'
            formatted_videos.append(formatted_video)
            
        return formatted_videos
    
    def _format_user_videos(self, videos: List[Dict], username: str, profile: Dict) -> List[Dict[str, Any]]:
        """Format user videos to match expected actor data format."""
        formatted_videos = []
        
        for video in videos:
            formatted_video = self._format_video_data(video)
            formatted_video['target_username'] = username
            formatted_video['scrape_type'] = 'user_videos'
            formatted_video['profile_data'] = profile
            formatted_videos.append(formatted_video)
            
        return formatted_videos
    
    def _format_hashtag_videos(self, videos: List[Dict], hashtag: str) -> List[Dict[str, Any]]:
        """Format hashtag videos to match expected actor data format."""
        formatted_videos = []
        
        for video in videos:
            formatted_video = self._format_video_data(video)
            formatted_video['target_hashtag'] = hashtag
            formatted_video['scrape_type'] = 'hashtag'
            formatted_videos.append(formatted_video)
            
        return formatted_videos
    
    def _format_video_data(self, video: Dict) -> Dict[str, Any]:
        """Format individual video data to match actor system expectations."""
        return {
            'aweme_id': video.get('id', video.get('aweme_id', '')),
            'id': video.get('id', video.get('aweme_id', '')),
            'desc': video.get('description', video.get('desc', '')),
            'description': video.get('description', video.get('desc', '')),
            'author': {
                'unique_id': video.get('author', {}).get('username', ''),
                'nickname': video.get('author', {}).get('nickname', ''),
                'avatar_thumb': {
                    'url_list': [video.get('author', {}).get('avatar', '')]
                },
                'follower_count': video.get('author', {}).get('followers', 0),
                'following_count': video.get('author', {}).get('following', 0),
                'aweme_count': video.get('author', {}).get('videos', 0),
                'verified': video.get('author', {}).get('verified', False)
            },
            'music': {
                'title': video.get('music', {}).get('title', ''),
                'author': video.get('music', {}).get('author', ''),
                'duration': video.get('music', {}).get('duration', 0)
            },
            'video': {
                'duration': video.get('duration', 0),
                'ratio': '9:16',
                'cover': {
                    'url_list': [video.get('thumbnail', '')]
                },
                'play_addr': {
                    'url_list': [video.get('video_url', '')]
                }
            },
            'statistics': {
                'digg_count': video.get('likes', 0),
                'share_count': video.get('shares', 0),
                'comment_count': video.get('comments', 0),
                'play_count': video.get('views', 0)
            },
            'create_time': video.get('timestamp', int(datetime.now().timestamp())),
            'hashtags': [
                {'name': tag} for tag in video.get('hashtags', [])
            ],
            'selenium_scraper': True,  # Flag to indicate this came from Selenium scraper
            'scraped_at': datetime.now().isoformat()
        }
    
    def cleanup(self):
        """Clean up WebDriver resources."""
        try:
            if self.driver:
                return_driver(self.driver)
                self.driver = None
                self.logger.info("WebDriver cleaned up successfully")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")