import logging
import json
import time
import random
from datetime import datetime, timedelta
from celery import shared_task
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import transaction
from .models import TikTokUserAccount, ActorTask, ActorScrapedData, TikTokSession
from .utils.tiktok_auth import TikTokAuthenticator
from .utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
from .utils.anti_detection import AntiDetectionManager
from .utils.actor_scraper import ActorTik<PERSON>okScraper as ActorScraper
from .utils.enhanced_scraper import EnhancedTikTokScraper
from .utils.data_processor import process_actor_data
from .utils.session_manager import SessionManager, EnhancedSessionManager
from .utils.proxy_manager import ProxyManager

logger = logging.getLogger(__name__)

@shared_task(bind=True, max_retries=3)
def enhanced_actor_login_task(self, user_id, tiktok_username, tiktok_password,
                             use_2fa=False, two_factor_code=None, proxy_config=None):
    """
    Enhanced Celery task for TikTok login with advanced anti-detection and proxy support
    """
    try:
        user = User.objects.get(id=user_id)
        retry_count = self.request.retries
        logger.info(f"Starting enhanced TikTok login for user {user_id}, username: {tiktok_username} (attempt {retry_count + 1})")

        # Get or create account
        account, created = TikTokUserAccount.objects.get_or_create(
            user=user,
            tiktok_username=tiktok_username,
            defaults={
                'is_active': True,
                'login_attempts': 0
            }
        )

        if created:
            logger.info(f"Created new TikTok account record for {tiktok_username}")

        # Check if account is blocked
        if account.is_blocked and account.blocked_until and account.blocked_until > timezone.now():
            remaining_time = (account.blocked_until - timezone.now()).total_seconds() / 60
            return {
                'success': False,
                'error': f'Account temporarily blocked. Try again in {remaining_time:.0f} minutes.',
                'blocked': True,
                'retry_after': account.blocked_until.isoformat()
            }

        # Store encrypted password
        if password != account.decrypt_password():
            account.encrypt_password(tiktok_password)
            account.save()

        # Initialize enhanced authenticator with proxy support
        authenticator = EnhancedTikTokAuthenticator(proxy_config=proxy_config)

        # Perform enhanced login
        login_result = authenticator.login(
            username=tiktok_username,
            password=tiktok_password,
            use_2fa=use_2fa,
            two_factor_code=two_factor_code,
            retry_count=retry_count,
            account_id=account.id
        )

        if login_result['success']:
            # Update account on successful login
            account.last_login = timezone.now()
            account.is_active = True
            account.is_blocked = False
            account.blocked_until = None
            account.login_attempts = 0

            # Set session expiration (24 hours from now)
            account.session_expires_at = timezone.now() + timedelta(hours=24)
            account.save()

            logger.info(f"Enhanced login successful for {tiktok_username}")

            return {
                'success': True,
                'message': 'Enhanced login successful',
                'account_id': account.id,
                'session_expires_at': account.session_expires_at.isoformat(),
                'strategy_used': login_result.get('strategy', 'unknown')
            }
        else:
            # Handle login failure
            account.increment_login_attempts()
            account.save()

            error_message = login_result.get('error', 'Unknown login error')
            logger.error(f"Enhanced login failed for {tiktok_username}: {error_message}")

            # Retry if not at max retries and error is recoverable
            if retry_count < self.max_retries and login_result.get('error_type') in ['rate_limit', 'captcha']:
                logger.info(f"Retrying enhanced login for {tiktok_username} (attempt {retry_count + 2})")
                raise self.retry(countdown=60 * (retry_count + 1))

            return {
                'success': False,
                'error': error_message,
                'error_type': login_result.get('error_type', 'unknown'),
                'strategies_attempted': login_result.get('strategies_attempted', []),
                'retry_count': retry_count
            }

    except Exception as e:
        logger.error(f"Enhanced login task failed: {str(e)}")
        return {
            'success': False,
            'error': f'Task execution failed: {str(e)}',
            'retry_count': retry_count
        }

# Twitter-specific Celery tasks
@shared_task(bind=True, max_retries=2)
def twitter_content_search_task(self, task_id):
    """
    Simple Celery task to search Twitter content by keywords and date range using Selenium.
    """
    try:
        from .models import ActorTask, ActorScrapedData
        from .scrapers.simple_twitter_scraper import SimpleTwitterScraper

        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()

        logger.info(f"Starting simple Twitter search task {task_id}")

        # Get task parameters
        keyword = task.keywords or "twitter"
        limit = task.max_items or 10
        start_date = task.start_date.strftime('%Y-%m-%d') if task.start_date else None
        end_date = task.end_date.strftime('%Y-%m-%d') if task.end_date else None
        account = task.actor_account

        logger.info(f"Searching Twitter for: '{keyword}', dates: {start_date} to {end_date}, limit: {limit}")

        # Initialize simple Twitter scraper
        scraper = SimpleTwitterScraper()

        # Perform simple Twitter search
        result = scraper.search_tweets(
            keyword=keyword,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

        # Close scraper
        scraper.close()

        if not result.get('success'):
            raise Exception(f"Twitter search failed: {result.get('error', 'Unknown error')}")

        tweets = result.get('tweets', [])

        # Save results to database
        items_saved = 0
        for tweet in tweets:
            try:
                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='twitter',
                    data_type='TWEET',
                    content=tweet,
                    actor_account=account,
                    account_username=account.platform_username,
                    platform_content_id=tweet.get('id_str', f"twitter_{items_saved + 1}"),
                    is_complete=True,
                    quality_score=1.0 if tweet.get('real_scraped', False) else 0.8
                )
                items_saved += 1
                logger.debug(f"Saved tweet {items_saved}: {tweet.get('text', 'N/A')[:50]}...")
            except Exception as e:
                logger.error(f"Failed to save tweet: {str(e)}")
                continue

        # Update task completion
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = items_saved
        task.save()

        logger.info(f"Simple Twitter search task {task_id} completed. Items saved: {items_saved}")

        return {
            'success': True,
            'task_id': task_id,
            'items_scraped': items_saved,
            'keyword': keyword,
            'start_date': start_date,
            'end_date': end_date,
            'scraping_time': result.get('scraping_time', 0),
            'message': f'Successfully scraped {items_saved} tweets for "{keyword}"'
        }

    except ActorTask.DoesNotExist:
        logger.error(f"Twitter task {task_id} not found")
        return {
            'success': False,
            'error': 'Task not found'
        }
    except Exception as e:
        logger.error(f"Twitter content search task {task_id} failed: {str(e)}")

        # Update task status to failed
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass

        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying Twitter task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60)

        return {
            'success': False,
            'error': f'Twitter task failed: {str(e)}'
        }

@shared_task(bind=True, max_retries=2)
def twitter_user_scrape_task(self, task_id):
    """
    Celery task to scrape a specific Twitter user's tweets.
    """
    try:
        from .models import ActorTask, ActorScrapedData
        from .engines.twitter_engine import TwitterEngine

        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()

        logger.info(f"Starting Twitter user scrape task {task_id}")

        # Initialize Twitter engine
        engine = TwitterEngine()
        account = task.actor_account

        # Get task parameters
        target_username = task.target_identifier
        limit = task.max_items or 10

        if not target_username:
            raise ValueError("target_identifier (username) is required for Twitter user scraping")

        logger.info(f"Scraping Twitter user: @{target_username}, limit: {limit}")

        # Perform Twitter user scraping
        results = engine.scrape_user_content(
            account=account,
            target_username=target_username,
            limit=limit
        )

        # Process and save results
        items_saved = 0
        for result in results:
            try:
                # Save to ActorScrapedData with correct field names
                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='twitter',
                    data_type='TWEET',  # Use uppercase as defined in choices
                    content=result,
                    actor_account=account,
                    account_username=account.platform_username,
                    platform_content_id=result.get('id_str', f"twitter_user_{items_saved + 1}"),
                    is_complete=True,
                    quality_score=1.0 if result.get('real_scraped', False) else 0.8
                )
                items_saved += 1
                logger.debug(f"Saved Twitter user data item {items_saved}: @{result.get('user', {}).get('screen_name', 'unknown')}")
            except Exception as e:
                logger.error(f"Failed to save Twitter user data item: {str(e)}")
                continue

        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = items_saved
        task.save()

        logger.info(f"Twitter user scrape task {task_id} completed successfully. Items saved: {items_saved}")

        return {
            'success': True,
            'task_id': task_id,
            'items_scraped': items_saved,
            'message': f'Successfully scraped {items_saved} tweets from @{target_username}'
        }

    except ActorTask.DoesNotExist:
        logger.error(f"Twitter user task {task_id} not found")
        return {
            'success': False,
            'error': 'Task not found'
        }
    except Exception as e:
        logger.error(f"Twitter user scrape task {task_id} failed: {str(e)}")

        # Update task status to failed
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass

        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying Twitter user task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60)

        return {
            'success': False,
            'error': f'Twitter user task failed: {str(e)}'
        }

@shared_task(bind=True, max_retries=2)
def twitter_feed_scrape_task(self, task_id):
    """
    Celery task to scrape Twitter feed/timeline.
    """
    try:
        from .models import ActorTask, ActorScrapedData
        from .engines.twitter_engine import TwitterEngine

        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()

        logger.info(f"Starting Twitter feed scrape task {task_id}")

        # Initialize Twitter engine
        engine = TwitterEngine()
        account = task.actor_account

        # Get task parameters
        limit = task.max_items or 10

        logger.info(f"Scraping Twitter feed for @{account.platform_username}, limit: {limit}")

        # Perform Twitter feed scraping
        results = engine.scrape_feed(
            account=account,
            limit=limit
        )

        # Process and save results
        items_saved = 0
        for result in results:
            try:
                # Save to ActorScrapedData with correct field names
                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='twitter',
                    data_type='TWEET',  # Use uppercase as defined in choices
                    content=result,
                    actor_account=account,
                    account_username=account.platform_username,
                    platform_content_id=result.get('id_str', f"twitter_feed_{items_saved + 1}"),
                    is_complete=True,
                    quality_score=1.0 if result.get('real_scraped', False) else 0.8
                )
                items_saved += 1
                logger.debug(f"Saved Twitter feed data item {items_saved}: {result.get('text', 'N/A')[:50]}")
            except Exception as e:
                logger.error(f"Failed to save Twitter feed data item: {str(e)}")
                continue

        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = items_saved
        task.save()

        logger.info(f"Twitter feed scrape task {task_id} completed successfully. Items saved: {items_saved}")

        return {
            'success': True,
            'task_id': task_id,
            'items_scraped': items_saved,
            'message': f'Successfully scraped {items_saved} Twitter feed items'
        }

    except ActorTask.DoesNotExist:
        logger.error(f"Twitter feed task {task_id} not found")
        return {
            'success': False,
            'error': 'Task not found'
        }
    except Exception as e:
        logger.error(f"Twitter feed scrape task {task_id} failed: {str(e)}")

        # Update task status to failed
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass

        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying Twitter feed task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60)

        return {
            'success': False,
            'error': f'Twitter feed task failed: {str(e)}'
        }

@shared_task(bind=True, max_retries=3)
def actor_login_task(self, user_id, tiktok_username, tiktok_password, use_2fa=False, two_factor_code=None, remember_session=True):
    """
    Enhanced Celery task to handle TikTok login with advanced anti-detection measures
    """
    driver = None
    try:
        user = User.objects.get(id=user_id)
        retry_count = self.request.retries
        logger.info(f"Starting TikTok login for user {user_id}, username: {tiktok_username} (attempt {retry_count + 1})")
        
        # Check if account exists and is blocked
        try:
            account = TikTokUserAccount.objects.get(
                user=user,
                tiktok_username=tiktok_username
            )
            
            # Initialize enhanced session manager
            session_manager = EnhancedSessionManager()
            
            # Check session rotation needs
            rotation_result = session_manager.rotate_session_if_needed(account.id)
            if rotation_result.get('rotated'):
                logger.info(f"Session rotated for account {account.id}: {rotation_result['reason']}")
                
                # If cooldown was applied, respect it
                if rotation_result.get('cooldown_applied'):
                    remaining_time = (account.blocked_until - timezone.now()).total_seconds() / 60 if account.blocked_until else 0
                    if remaining_time > 0:
                        logger.warning(f"Account {tiktok_username} is in cooldown for {remaining_time:.0f} more minutes")
                        return {
                            'success': False,
                            'error': f'Account in cooldown for {remaining_time:.0f} minutes.',
                            'blocked': True,
                            'retry_after': account.blocked_until.isoformat()
                        }
            
            # Check if account is temporarily blocked
            if account.is_blocked and account.blocked_until:
                if account.blocked_until > timezone.now():
                    remaining_time = (account.blocked_until - timezone.now()).total_seconds() / 60
                    logger.warning(f"Account {tiktok_username} is blocked for {remaining_time:.0f} more minutes")
                    return {
                        'success': False,
                        'error': f'Account temporarily blocked. Try again in {remaining_time:.0f} minutes.',
                        'blocked': True,
                        'retry_after': account.blocked_until.isoformat()
                    }
                else:
                    # Block period expired, reset
                    account.is_blocked = False
                    account.blocked_until = None
                    account.save()
                    
        except TikTokUserAccount.DoesNotExist:
            account = None
            session_manager = EnhancedSessionManager()
        
        # Initialize authenticator with anti-detection
        authenticator = TikTokAuthenticator()
        anti_detection = AntiDetectionManager()
        
        # Set account reference for blocking logic
        if account:
            authenticator.account = account
        
        # Apply anti-detection measures
        driver = anti_detection.setup_driver()
        
        try:
            # Perform enhanced login with retry count
            login_result = authenticator.login(
                driver=driver,
                username=tiktok_username,
                password=tiktok_password,
                use_2fa=use_2fa,
                two_factor_code=two_factor_code,
                retry_count=retry_count
            )
            
            if login_result['success']:
                # Create or update TikTok account
                account, created = TikTokUserAccount.objects.get_or_create(
                    user=user,
                    tiktok_username=tiktok_username,
                    defaults={
                        'encrypted_session_data': json.dumps(login_result['session_data']),
                        'is_active': True,
                        'session_expires_at': timezone.now() + timedelta(days=30 if remember_session else 1),
                        'login_attempts': 0
                    }
                )
                
                if not created:
                    # Update existing account
                    account.encrypted_session_data = json.dumps(login_result['session_data'])
                    account.is_active = True
                    account.session_expires_at = timezone.now() + timedelta(days=30 if remember_session else 1)
                    account.login_attempts = 0
                    account.is_blocked = False
                    account.blocked_until = None
                    account.save()
                
                # Save session data with enhanced manager
                enhanced_session_data = {
                    'cookies': login_result.get('cookies', []),
                    'local_storage': login_result.get('local_storage', {}),
                    'session_storage': login_result.get('session_storage', {}),
                    'user_agent': driver.execute_script("return navigator.userAgent;") if driver else '',
                    'anti_bot_score': login_result.get('anti_bot_score', 75.0),
                    'captcha_count': login_result.get('captcha_count', 0),
                    'login_timestamp': timezone.now().isoformat(),
                    'browser_fingerprint': login_result.get('browser_fingerprint', ''),
                    'ip_address': login_result.get('ip_address', '')
                }
                
                session_manager.save_session(account.id, enhanced_session_data)
                
                # Create session record
                TikTokSession.objects.create(
                    tiktok_account=account,
                    is_healthy=True,
                    session_data=login_result.get('session_metadata', {})
                )
                
                # Get health score for monitoring
                health_score = session_manager.get_account_health_score(account.id)
                
                logger.info(f"Successfully logged in TikTok account: {tiktok_username} with health score {health_score.get('overall_score', 0):.2f}")
                return {
                    'success': True,
                    'message': 'Successfully logged in to TikTok',
                    'account_id': account.id,
                    'retry_count': retry_count,
                    'health_score': health_score.get('overall_score', 0),
                    'recommendations': health_score.get('recommendations', [])
                }
            else:
                # Handle login failure with enhanced error handling
                error_message = login_result.get('error', 'Unknown login error')
                is_bot_detected = login_result.get('bot_detected', False)
                is_rate_limited = login_result.get('rate_limited', False)
                is_captcha_timeout = login_result.get('captcha_timeout', False)
                
                # Update login attempts if account exists
                if account:
                    account.increment_login_attempts()
                    
                    # Apply longer blocks for bot detection or rate limiting
                    if is_bot_detected or is_rate_limited:
                        import random
                        account.is_blocked = True
                        # Longer block for bot detection (4-8 hours)
                        block_hours = random.uniform(4, 8) if is_bot_detected else random.uniform(1, 3)
                        account.blocked_until = timezone.now() + timedelta(hours=block_hours)
                        account.save()
                        logger.warning(f"Account {tiktok_username} blocked for {block_hours:.1f} hours due to {'bot detection' if is_bot_detected else 'rate limiting'}")
                
                logger.error(f"TikTok login failed for {tiktok_username}: {error_message}")
                
                # Enhanced retry logic with strategy awareness
                should_retry = True
                retry_delay = 180  # 3 minutes base delay (reduced from 5)
                
                # Check if all strategies were exhausted
                strategy_exhausted = login_result.get('strategy_exhausted', False)
                
                if is_bot_detected:
                    # Still retry but with longer delay for bot detection
                    retry_delay = 1200  # 20 minutes for bot detection
                    if self.request.retries >= 2:  # Don't retry after 2 attempts for bot detection
                        should_retry = False
                        return {
                            'success': False,
                            'error': f'Bot detection triggered after multiple strategies: {error_message}',
                            'bot_detected': True,
                            'retry_after': (timezone.now() + timedelta(hours=2)).isoformat()
                        }
                elif is_rate_limited:
                    # Moderate delay for rate limiting since we have multiple strategies
                    retry_delay = 900  # 15 minutes (reduced from 30)
                elif is_captcha_timeout:
                    # Shorter delay for CAPTCHA timeout
                    retry_delay = 300  # 5 minutes (reduced from 10)
                elif strategy_exhausted:
                    # All strategies failed, use longer delay
                    retry_delay = 600  # 10 minutes when all strategies fail
                
                # Retry logic with intelligent delays
                if should_retry and self.request.retries < self.max_retries:
                    import random
                    # Less aggressive exponential backoff since we have multiple strategies
                    delay_multiplier = 1.5 ** self.request.retries  # Reduced from 2
                    jitter = random.uniform(0.9, 1.1)  # Reduced jitter range
                    actual_delay = min(retry_delay * delay_multiplier * jitter, 1800)  # Cap at 30 minutes
                    
                    logger.info(f"Retrying TikTok login task in {actual_delay/60:.1f} minutes (attempt {self.request.retries + 1}/{self.max_retries})")
                    logger.info(f"Previous attempt used {login_result.get('strategies_attempted', 'unknown')} strategies")
                    raise self.retry(countdown=int(actual_delay), exc=Exception(error_message))
                
                return {
                    'success': False,
                    'error': error_message,
                    'retry_count': retry_count,
                    'final_attempt': self.request.retries >= self.max_retries
                }
        
        finally:
            # Clean up driver
            if driver:
                driver.quit()
    
    except Exception as exc:
        logger.error(f"Error in TikTok login task: {str(exc)}")
        
        # Enhanced retry logic
        if self.request.retries < self.max_retries:
            import random
            # Progressive delay with randomization
            base_delay = 300  # 5 minutes
            delay_multiplier = 2 ** self.request.retries
            jitter = random.uniform(0.8, 1.2)
            retry_delay = min(base_delay * delay_multiplier * jitter, 3600)  # Cap at 1 hour
            
            logger.info(f"Retrying TikTok login task in {retry_delay/60:.1f} minutes due to exception (attempt {self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=int(retry_delay), exc=exc)
        
        return {
            'success': False,
            'error': f'Login failed after {self.max_retries} attempts: {str(exc)}',
            'final_attempt': True
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_my_videos_task(self, task_id):
    """
    Enhanced Celery task to scrape user's own TikTok videos using Selenium
    """
    try:
        from .models import ActorTask, ActorScrapedData
        from .engines.tiktok_engine import TikTokEngine

        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()

        logger.info(f"Starting enhanced TikTok my videos scraping task {task_id}")

        # Initialize TikTok engine
        engine = TikTokEngine()
        account = task.actor_account

        # Get task parameters
        limit = task.max_items or 50

        logger.info(f"Scraping my videos for @{account.platform_username}, limit: {limit}")

        # Perform TikTok my content scraping using Selenium
        results = engine.scrape_my_content(
            account=account,
            limit=limit
        )

        # Process and save results
        items_saved = 0
        for result in results:
            try:
                # Save to ActorScrapedData with correct field names
                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='tiktok',
                    data_type='VIDEO',  # Use uppercase as defined in choices
                    content=result,
                    actor_account=account,
                    account_username=account.platform_username,
                    platform_content_id=result.get('id', f"tiktok_my_{items_saved + 1}"),
                    is_complete=True,
                    quality_score=1.0 if result.get('real_scraped', False) else 0.8
                )
                items_saved += 1
                logger.debug(f"Saved TikTok my video data item {items_saved}: {result.get('desc', 'N/A')[:50]}")
            except Exception as e:
                logger.error(f"Failed to save TikTok my video data item: {str(e)}")
                continue

        # Update task completion
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = items_saved
        task.save()

        logger.info(f"TikTok my videos task {task_id} completed successfully. Items saved: {items_saved}")

        return {
            'success': True,
            'task_id': task_id,
            'items_scraped': items_saved,
            'message': f'Successfully scraped {items_saved} TikTok my videos'
        }
    
    except Exception as exc:
        logger.error(f"Error in my videos scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_my_followers_task(self, task_id):
    """
    Celery task to scrape user's followers
    """
    try:
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()
        
        logger.info(f"Starting my followers scraping task {task_id}")
        
        # Initialize scraper
        scraper = ActorScraper(task.tiktok_account)
        
        # Get task parameters
        params = task.task_parameters
        limit = params.get('limit', 100)
        include_details = params.get('include_details', True)
        
        # Perform scraping
        followers_data = scraper.scrape_my_followers(
            limit=limit,
            include_details=include_details
        )
        
        # Process and save data
        for follower_data in followers_data:
            process_actor_data(task, follower_data, 'user')
        
        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = len(followers_data)
        task.save()
        
        logger.info(f"Completed my followers scraping task {task_id}, scraped {len(followers_data)} followers")
        return {
            'success': True,
            'items_scraped': len(followers_data)
        }
    
    except Exception as exc:
        logger.error(f"Error in my followers scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_my_following_task(self, task_id):
    """
    Celery task to scrape users that the account is following
    """
    try:
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()
        
        logger.info(f"Starting my following scraping task {task_id}")
        
        # Initialize scraper
        scraper = ActorScraper(task.tiktok_account)
        
        # Get task parameters
        params = task.task_parameters
        limit = params.get('limit', 100)
        include_details = params.get('include_details', True)
        
        # Perform scraping
        following_data = scraper.scrape_my_following(
            limit=limit,
            include_details=include_details
        )
        
        # Process and save data
        for following_user_data in following_data:
            process_actor_data(task, following_user_data, 'user')
        
        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = len(following_data)
        task.save()
        
        logger.info(f"Completed my following scraping task {task_id}, scraped {len(following_data)} users")
        return {
            'success': True,
            'items_scraped': len(following_data)
        }
    
    except Exception as exc:
        logger.error(f"Error in my following scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_my_likes_task(self, task_id):
    """
    Celery task to scrape videos liked by the user
    """
    try:
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()
        
        logger.info(f"Starting my likes scraping task {task_id}")
        
        # Initialize scraper
        scraper = ActorScraper(task.tiktok_account)
        
        # Get task parameters
        params = task.task_parameters
        limit = params.get('limit', 100)
        
        # Perform scraping
        liked_videos_data = scraper.scrape_my_likes(limit=limit)
        
        # Process and save data
        for video_data in liked_videos_data:
            process_actor_data(task, video_data, 'video')
        
        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = len(liked_videos_data)
        task.save()
        
        logger.info(f"Completed my likes scraping task {task_id}, scraped {len(liked_videos_data)} liked videos")
        return {
            'success': True,
            'items_scraped': len(liked_videos_data)
        }
    
    except Exception as exc:
        logger.error(f"Error in my likes scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_feed_task(self, task_id):
    """
    Celery task to scrape user's personalized feed
    """
    try:
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()
        
        logger.info(f"Starting feed scraping task {task_id}")
        
        # Initialize scraper
        scraper = ActorScraper(task.tiktok_account)
        
        # Get task parameters
        params = task.task_parameters
        limit = params.get('limit', 50)
        feed_type = params.get('feed_type', 'for_you')  # 'for_you' or 'following'
        
        # Perform scraping
        feed_data = scraper.scrape_feed(
            limit=limit,
            feed_type=feed_type
        )
        
        # Process and save data
        for video_data in feed_data:
            process_actor_data(task, video_data, 'video')
        
        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = len(feed_data)
        task.save()
        
        logger.info(f"Completed feed scraping task {task_id}, scraped {len(feed_data)} videos")
        return {
            'success': True,
            'items_scraped': len(feed_data)
        }
    
    except Exception as exc:
        logger.error(f"Error in feed scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_targeted_user_task(self, task_id):
    """
    Enhanced Celery task to scrape a specific TikTok user's profile and videos using Selenium
    """
    try:
        from .models import ActorTask, ActorScrapedData
        from .engines.tiktok_engine import TikTokEngine

        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()

        logger.info(f"Starting enhanced TikTok targeted user scraping task {task_id}")

        # Initialize TikTok engine
        engine = TikTokEngine()
        account = task.actor_account

        # Get task parameters
        target_username = task.target_identifier
        limit = task.max_items or 50

        if not target_username:
            raise ValueError("target_identifier is required for targeted user scraping")

        logger.info(f"Scraping TikTok user @{target_username}, limit: {limit}")

        # Perform TikTok user content scraping using Selenium
        results = engine.scrape_user_content(
            account=account,
            target_username=target_username,
            limit=limit
        )

        # Process and save results
        items_saved = 0
        for result in results:
            try:
                # Save to ActorScrapedData with correct field names
                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='tiktok',
                    data_type='VIDEO',  # Use uppercase as defined in choices
                    content=result,
                    actor_account=account,
                    account_username=account.platform_username,
                    platform_content_id=result.get('id', f"tiktok_user_{items_saved + 1}"),
                    is_complete=True,
                    quality_score=1.0 if result.get('real_scraped', False) else 0.8
                )
                items_saved += 1
                logger.debug(f"Saved TikTok user video data item {items_saved}: @{target_username} - {result.get('desc', 'N/A')[:50]}")
            except Exception as e:
                logger.error(f"Failed to save TikTok user video data item: {str(e)}")
                continue

        # Update task completion
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = items_saved
        task.save()

        logger.info(f"TikTok targeted user task {task_id} completed successfully. Items saved: {items_saved}")

        return {
            'success': True,
            'task_id': task_id,
            'items_scraped': items_saved,
            'message': f'Successfully scraped {items_saved} videos from @{target_username}'
        }
    
    except Exception as exc:
        logger.error(f"Error in targeted user scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_hashtag_task(self, task_id):
    """
    Enhanced Celery task to scrape TikTok videos from a specific hashtag using Selenium
    """
    try:
        from .models import ActorTask, ActorScrapedData
        from .engines.tiktok_engine import TikTokEngine

        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()

        logger.info(f"Starting enhanced TikTok hashtag scraping task {task_id}")

        # Initialize TikTok engine
        engine = TikTokEngine()
        account = task.actor_account

        # Get task parameters
        keywords = task.keywords or ""
        hashtag = keywords.replace('#', '').strip()  # Remove # if present
        limit = task.max_items or 50

        if not hashtag:
            raise ValueError("keywords/hashtag is required for hashtag scraping")

        logger.info(f"Scraping TikTok hashtag #{hashtag}, limit: {limit}")

        # Perform TikTok hashtag content scraping using Selenium
        # Use search_content with hashtag as keyword
        results = engine.search_content(
            account=account,
            keywords=[f"#{hashtag}"],
            limit=limit
        )

        # Process and save results
        items_saved = 0
        for result in results:
            try:
                # Save to ActorScrapedData with correct field names
                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='tiktok',
                    data_type='VIDEO',  # Use uppercase as defined in choices
                    content=result,
                    actor_account=account,
                    account_username=account.platform_username,
                    platform_content_id=result.get('id', f"tiktok_hashtag_{items_saved + 1}"),
                    is_complete=True,
                    quality_score=1.0 if result.get('real_scraped', False) else 0.8
                )
                items_saved += 1
                logger.debug(f"Saved TikTok hashtag video data item {items_saved}: #{hashtag} - {result.get('desc', 'N/A')[:50]}")
            except Exception as e:
                logger.error(f"Failed to save TikTok hashtag video data item: {str(e)}")
                continue

        # Update task completion
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = items_saved
        task.save()

        logger.info(f"TikTok hashtag task {task_id} completed successfully. Items saved: {items_saved}")

        return {
            'success': True,
            'task_id': task_id,
            'items_scraped': items_saved,
            'message': f'Successfully scraped {items_saved} videos from #{hashtag}'
        }
    
    except Exception as exc:
        logger.error(f"Error in hashtag scraping task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def actor_scrape_competitor_task(self, task_id):
    """
    Celery task to perform competitor analysis
    """
    try:
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.save()
        
        logger.info(f"Starting competitor analysis task {task_id}")
        
        # Initialize scraper
        scraper = ActorScraper(task.tiktok_account)
        
        # Get task parameters
        params = task.task_parameters
        competitor_usernames = params.get('competitor_usernames', [])
        analysis_depth = params.get('analysis_depth', 'basic')  # 'basic' or 'detailed'
        
        if not competitor_usernames:
            raise ValueError("competitor_usernames is required for competitor analysis")
        
        total_items = 0
        
        # Perform analysis for each competitor
        for username in competitor_usernames:
            competitor_data = scraper.analyze_competitor(
                username=username,
                analysis_depth=analysis_depth
            )
            
            # Process and save data
            process_actor_data(task, competitor_data, 'competitor_analysis')
            total_items += 1
        
        # Update task status
        task.status = 'COMPLETED'
        task.completed_at = timezone.now()
        task.items_scraped = total_items
        task.save()
        
        logger.info(f"Completed competitor analysis task {task_id}, analyzed {total_items} competitors")
        return {
            'success': True,
            'items_scraped': total_items
        }
    
    except Exception as exc:
        logger.error(f"Error in competitor analysis task {task_id}: {str(exc)}")
        
        # Update task status
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(exc)
            task.completed_at = timezone.now()
            task.save()
        except:
            pass
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def refresh_tiktok_session_task(self, account_id):
    """
    Celery task to refresh TikTok session for an account
    """
    try:
        logger.info(f"Starting session refresh for account {account_id}")
        
        account = TikTokUserAccount.objects.get(id=account_id)
        
        # Initialize session manager
        session_manager = SessionManager(account)
        
        # Refresh session
        refresh_result = session_manager.refresh_session()
        
        if refresh_result['success']:
            # Update account with new session data
            account.encrypted_session_data = json.dumps(refresh_result['session_data'])
            account.session_expires_at = timezone.now() + timedelta(days=30)
            account.save()
            
            # Create new session record
            TikTokSession.objects.create(
                tiktok_account=account,
                is_healthy=True,
                session_data=refresh_result.get('session_metadata', {})
            )
            
            logger.info(f"Successfully refreshed session for account {account_id}")
            return {
                'success': True,
                'message': 'Session refreshed successfully'
            }
        else:
            # Mark account as inactive if refresh failed
            account.is_active = False
            account.save()
            
            logger.error(f"Failed to refresh session for account {account_id}: {refresh_result.get('error')}")
            return {
                'success': False,
                'error': refresh_result.get('error', 'Unknown refresh error')
            }
    
    except Exception as exc:
        logger.error(f"Error in session refresh task for account {account_id}: {str(exc)}")
        
        # Retry logic
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
        return {
            'success': False,
            'error': str(exc)
        }

@shared_task
def select_best_account_for_task(user_id, task_type='general'):
    """
    Select the best account for a task based on health scores and availability
    """
    try:
        logger.info(f"Selecting best account for user {user_id}, task type: {task_type}")
        
        # Get all active accounts for the user
        available_accounts = list(
            TikTokUserAccount.objects.filter(
                user_id=user_id,
                is_active=True
            ).values_list('id', flat=True)
        )
        
        if not available_accounts:
            logger.warning(f"No active accounts found for user {user_id}")
            return {
                'success': False,
                'error': 'No active accounts available',
                'selected_account_id': None
            }
        
        # Use enhanced session manager to select best account
        session_manager = EnhancedSessionManager()
        best_account_id = session_manager.select_best_account_for_task(available_accounts)
        
        if best_account_id:
            # Get health score for the selected account
            health_score = session_manager.get_account_health_score(best_account_id)
            
            logger.info(f"Selected account {best_account_id} with health score {health_score.get('overall_score', 0):.2f}")
            return {
                'success': True,
                'selected_account_id': best_account_id,
                'health_score': health_score.get('overall_score', 0),
                'risk_level': health_score.get('risk_level', 'unknown'),
                'recommendations': health_score.get('recommendations', [])
            }
        else:
            logger.warning(f"No suitable account found for user {user_id}")
            return {
                'success': False,
                'error': 'No suitable account found (all accounts may be in cooldown or unhealthy)',
                'selected_account_id': None
            }
    
    except Exception as exc:
        logger.error(f"Error selecting best account for user {user_id}: {str(exc)}")
        return {
            'success': False,
            'error': str(exc),
            'selected_account_id': None
        }

@shared_task
def monitor_account_health():
    """
    Periodic task to monitor account health and perform maintenance
    """
    try:
        logger.info("Starting account health monitoring")
        
        session_manager = EnhancedSessionManager()
        
        # Get all active accounts
        active_accounts = TikTokUserAccount.objects.filter(is_active=True)
        
        health_report = {
            'total_accounts': active_accounts.count(),
            'healthy_accounts': 0,
            'unhealthy_accounts': 0,
            'rotated_sessions': 0,
            'applied_cooldowns': 0,
            'recommendations': []
        }
        
        for account in active_accounts:
            try:
                # Check if session rotation is needed
                rotation_result = session_manager.rotate_session_if_needed(account.id)
                
                if rotation_result.get('rotated'):
                    health_report['rotated_sessions'] += 1
                    logger.info(f"Rotated session for account {account.id}: {rotation_result['reason']}")
                    
                    if rotation_result.get('cooldown_applied'):
                        health_report['applied_cooldowns'] += 1
                
                # Get health score
                health_score = session_manager.get_account_health_score(account.id)
                overall_score = health_score.get('overall_score', 0)
                
                if overall_score >= 0.6:
                    health_report['healthy_accounts'] += 1
                else:
                    health_report['unhealthy_accounts'] += 1
                    
                    # Add recommendations for unhealthy accounts
                    recommendations = health_score.get('recommendations', [])
                    if recommendations:
                        health_report['recommendations'].extend([
                            f"Account {account.id}: {rec}" for rec in recommendations[:2]  # Limit to 2 per account
                        ])
            
            except Exception as e:
                logger.warning(f"Error monitoring account {account.id}: {str(e)}")
                continue
        
        logger.info(f"Health monitoring completed: {health_report}")
        return health_report
    
    except Exception as exc:
        logger.error(f"Error in health monitoring task: {str(exc)}")
        return {
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=3)
def multi_account_login_task(self, user_id, account_credentials_list, headless=True):
    """
    Enhanced multi-account login task with automatic account switching on bot detection
    
    Args:
        user_id: User ID
        account_credentials_list: List of dicts with 'username', 'email', 'password'
        headless: Whether to run in headless mode
    """
    driver = None
    successful_logins = []
    failed_logins = []
    
    try:
        user = User.objects.get(id=user_id)
        logger.info(f"Starting multi-account login for user {user_id} with {len(account_credentials_list)} accounts")
        
        # Initialize session manager and anti-detection
        session_manager = EnhancedSessionManager()
        anti_detection = AntiDetectionManager()
        
        # Setup driver once for all accounts
        driver = anti_detection.setup_driver(headless=headless)
        
        for i, credentials in enumerate(account_credentials_list):
            username = credentials.get('username') or credentials.get('email')
            email = credentials.get('email')
            password = credentials.get('password')
            
            if not username or not password:
                failed_logins.append({
                    'username': username,
                    'error': 'Missing username or password'
                })
                continue
                
            logger.info(f"Attempting login {i+1}/{len(account_credentials_list)}: {username}")
            
            try:
                # Check if account exists and is blocked
                try:
                    account = TikTokUserAccount.objects.get(
                        user=user,
                        tiktok_username=username
                    )
                    
                    # Skip if account is currently blocked
                    if account.is_blocked and account.blocked_until and account.blocked_until > timezone.now():
                        remaining_time = (account.blocked_until - timezone.now()).total_seconds() / 60
                        logger.warning(f"Skipping blocked account {username} (blocked for {remaining_time:.0f} more minutes)")
                        failed_logins.append({
                            'username': username,
                            'error': f'Account blocked for {remaining_time:.0f} minutes',
                            'blocked': True
                        })
                        continue
                        
                except TikTokUserAccount.DoesNotExist:
                    account = None
                
                # Initialize authenticator
                authenticator = TikTokAuthenticator()
                if account:
                    authenticator.account = account
                
                # Attempt login
                login_result = authenticator.login(
                    driver=driver,
                    username=username,
                    password=password,
                    use_2fa=False,
                    retry_count=0
                )
                
                if login_result['success']:
                    # Create or update account
                    account, created = TikTokUserAccount.objects.get_or_create(
                        user=user,
                        tiktok_username=username,
                        defaults={
                            'email': email,
                            'encrypted_session_data': json.dumps(login_result['session_data']),
                            'is_active': True,
                            'session_expires_at': timezone.now() + timedelta(days=30),
                            'login_attempts': 0
                        }
                    )
                    
                    if not created:
                        # Update existing account
                        if email:
                            account.email = email
                        account.encrypt_password(password)
                        account.encrypted_session_data = json.dumps(login_result['session_data'])
                        account.is_active = True
                        account.session_expires_at = timezone.now() + timedelta(days=30)
                        account.login_attempts = 0
                        account.is_blocked = False
                        account.blocked_until = None
                        account.save()
                    else:
                        # Set password for new account
                        account.encrypt_password(password)
                        account.save()
                    
                    # Save enhanced session data
                    enhanced_session_data = {
                        'cookies': login_result.get('cookies', []),
                        'local_storage': login_result.get('local_storage', {}),
                        'session_storage': login_result.get('session_storage', {}),
                        'user_agent': driver.execute_script("return navigator.userAgent;") if driver else '',
                        'anti_bot_score': login_result.get('anti_bot_score', 75.0),
                        'captcha_count': login_result.get('captcha_count', 0),
                        'login_timestamp': timezone.now().isoformat(),
                        'browser_fingerprint': login_result.get('browser_fingerprint', ''),
                        'ip_address': login_result.get('ip_address', '')
                    }
                    
                    session_manager.save_session(account.id, enhanced_session_data)
                    
                    # Create session record
                    TikTokSession.objects.create(
                        tiktok_account=account,
                        is_healthy=True,
                        session_data=login_result.get('session_metadata', {})
                    )
                    
                    successful_logins.append({
                        'username': username,
                        'account_id': account.id,
                        'health_score': login_result.get('anti_bot_score', 75.0)
                    })
                    
                    logger.info(f"Successfully logged in account: {username}")
                    
                else:
                    # Handle login failure
                    error_message = login_result.get('error', 'Unknown login error')
                    is_bot_detected = login_result.get('bot_detected', False)
                    is_rate_limited = login_result.get('rate_limited', False)
                    
                    # Update account if it exists
                    if account:
                        account.increment_login_attempts()
                        
                        # Apply blocks for bot detection or rate limiting
                        if is_bot_detected or is_rate_limited:
                            account.is_blocked = True
                            block_hours = random.uniform(4, 8) if is_bot_detected else random.uniform(1, 3)
                            account.blocked_until = timezone.now() + timedelta(hours=block_hours)
                            account.save()
                            logger.warning(f"Account {username} blocked for {block_hours:.1f} hours due to {'bot detection' if is_bot_detected else 'rate limiting'}")
                    
                    failed_logins.append({
                        'username': username,
                        'error': error_message,
                        'bot_detected': is_bot_detected,
                        'rate_limited': is_rate_limited
                    })
                    
                    logger.error(f"Login failed for {username}: {error_message}")
                    
                    # If bot detected, wait before trying next account
                    if is_bot_detected:
                        logger.info("Bot detected, waiting 30 seconds before next account...")
                        time.sleep(30)
                        
                        # Refresh driver to avoid detection carryover
                        if driver:
                            driver.quit()
                        driver = anti_detection.setup_driver(headless=headless)
                        
            except Exception as account_exc:
                logger.error(f"Error processing account {username}: {str(account_exc)}")
                failed_logins.append({
                    'username': username,
                    'error': str(account_exc)
                })
                continue
        
        # Summary
        logger.info(f"Multi-account login completed: {len(successful_logins)} successful, {len(failed_logins)} failed")
        
        return {
            'success': True,
            'successful_logins': successful_logins,
            'failed_logins': failed_logins,
            'total_accounts': len(account_credentials_list),
            'success_rate': len(successful_logins) / len(account_credentials_list) if account_credentials_list else 0
        }
        
    except Exception as exc:
        logger.error(f"Error in multi-account login task: {str(exc)}")
        
        # Retry logic
        if self.request.retries < self.max_retries:
            retry_delay = 300 * (2 ** self.request.retries)  # Progressive delay
            logger.info(f"Retrying multi-account login in {retry_delay/60:.1f} minutes")
            raise self.retry(countdown=retry_delay, exc=exc)
        
        return {
            'success': False,
            'error': str(exc),
            'successful_logins': successful_logins,
            'failed_logins': failed_logins
        }
        
    finally:
        # Clean up driver
        if driver:
            driver.quit()

@shared_task
def cleanup_expired_sessions():
    """
    Periodic task to clean up expired sessions and inactive accounts
    """
    try:
        logger.info("Starting cleanup of expired sessions")
        
        # Use enhanced session manager for cleanup
        session_manager = EnhancedSessionManager()
        cleanup_count = session_manager.cleanup_expired_sessions()
        
        # Mark expired accounts as inactive
        expired_accounts = TikTokUserAccount.objects.filter(
            is_active=True,
            session_expires_at__lt=timezone.now()
        )
        
        expired_count = expired_accounts.count()
        expired_accounts.update(is_active=False)
        
        # Cancel running tasks for expired accounts
        cancelled_tasks = ActorTask.objects.filter(
            tiktok_account__in=expired_accounts,
            status__in=['PENDING', 'RUNNING']
        )
        
        cancelled_count = cancelled_tasks.count()
        cancelled_tasks.update(status='CANCELLED')
        
        # Clean up old session records (keep last 100 per account)
        from django.db.models import Window, F
        from django.db.models.functions import RowNumber
        
        old_sessions = TikTokSession.objects.annotate(
            row_number=Window(
                expression=RowNumber(),
                partition_by=[F('tiktok_account')],
                order_by=F('created_at').desc()
            )
        ).filter(row_number__gt=100)
        
        old_sessions_count = old_sessions.count()
        old_sessions.delete()
        
        logger.info(f"Cleanup completed: {expired_count} accounts expired, {cancelled_count} tasks cancelled, {old_sessions_count} old sessions deleted, {cleanup_count} sessions cleaned by enhanced manager")
        
        return {
            'expired_accounts': expired_count,
            'cancelled_tasks': cancelled_count,
            'deleted_sessions': old_sessions_count,
            'enhanced_cleanup_count': cleanup_count
        }
    
    except Exception as exc:
        logger.error(f"Error in cleanup task: {str(exc)}")
        return {
            'error': str(exc)
        }

@shared_task(bind=True, max_retries=2)
def enhanced_scraping_task(self, user_id, account_id, scraping_config):
    """
    Enhanced scraping task with advanced anti-detection and comprehensive data extraction

    Args:
        user_id: User ID
        account_id: TikTok account ID to use for scraping
        scraping_config: Configuration dictionary with scraping parameters
    """
    scraper = None
    try:
        user = User.objects.get(id=user_id)
        account = TikTokUserAccount.objects.get(id=account_id, user=user)
        retry_count = self.request.retries

        logger.info(f"Starting enhanced scraping task for user {user_id}, account {account_id} (attempt {retry_count + 1})")

        # Validate account session
        if not account.is_session_valid():
            return {
                'success': False,
                'error': 'Account session is invalid or expired',
                'requires_login': True
            }

        # Extract scraping parameters
        scrape_type = scraping_config.get('type', 'profile')  # profile, video, trending, search
        target = scraping_config.get('target')  # username, video_id, search_query
        options = scraping_config.get('options', {})
        proxy_config = scraping_config.get('proxy_config')
        rate_limit_config = scraping_config.get('rate_limit_config')

        # Initialize enhanced scraper
        scraper = EnhancedTikTokScraper(
            proxy_config=proxy_config,
            rate_limit_config=rate_limit_config
        )

        # Login and prepare scraper
        login_result = scraper.login_and_prepare(
            username=account.tiktok_username,
            password=account.decrypt_password(),
            account_id=account.id
        )

        if not login_result['success']:
            return {
                'success': False,
                'error': f'Scraper login failed: {login_result.get("error")}',
                'requires_login': True
            }

        # Perform scraping based on type
        scraping_result = None

        if scrape_type == 'profile':
            scraping_result = scraper.scrape_user_profile(
                username=target,
                include_videos=options.get('include_videos', True),
                video_limit=options.get('video_limit', 50)
            )

        elif scrape_type == 'video':
            username = options.get('username')
            if not username:
                return {'success': False, 'error': 'Username required for video scraping'}

            scraping_result = scraper.scrape_video_details(
                username=username,
                video_id=target
            )

        elif scrape_type == 'trending':
            scraping_result = scraper.scrape_trending_videos(
                limit=options.get('limit', 100),
                category=options.get('category')
            )

        elif scrape_type == 'search':
            scraping_result = scraper.search_content(
                query=target,
                content_type=options.get('content_type', 'videos'),
                limit=options.get('limit', 50)
            )

        else:
            return {'success': False, 'error': f'Unsupported scraping type: {scrape_type}'}

        if not scraping_result or not scraping_result.get('success'):
            error_msg = scraping_result.get('error', 'Unknown scraping error') if scraping_result else 'No scraping result'

            # Retry on rate limit or temporary errors
            if 'rate limit' in error_msg.lower() and retry_count < self.max_retries:
                logger.info(f"Retrying scraping task due to rate limit (attempt {retry_count + 2})")
                raise self.retry(countdown=300)  # 5 minutes

            return {
                'success': False,
                'error': error_msg,
                'retry_count': retry_count
            }

        # Process and store scraped data
        scraped_data = scraping_result['data']

        # Create ActorScrapedData record
        actor_data = ActorScrapedData.objects.create(
            user=user,
            account=account,
            data_type=scrape_type,
            target_identifier=target,
            raw_data=scraped_data,
            scraped_at=timezone.now(),
            metadata={
                'scraping_config': scraping_config,
                'retry_count': retry_count,
                'scraped_at': scraping_result.get('scraped_at')
            }
        )

        # Process data if processor is available
        try:
            processed_data = process_actor_data(scraped_data, scrape_type)
            actor_data.processed_data = processed_data
            actor_data.save()
        except Exception as e:
            logger.warning(f"Data processing failed: {str(e)}")

        # Update account last activity
        account.last_login = timezone.now()
        account.save()

        logger.info(f"Enhanced scraping completed successfully for {scrape_type}: {target}")

        return {
            'success': True,
            'data_id': actor_data.id,
            'scrape_type': scrape_type,
            'target': target,
            'data_count': len(scraped_data) if isinstance(scraped_data, list) else 1,
            'scraped_at': actor_data.scraped_at.isoformat()
        }

    except Exception as exc:
        logger.error(f"Enhanced scraping task failed: {str(exc)}")

        # Retry on recoverable errors
        if retry_count < self.max_retries and any(keyword in str(exc).lower() for keyword in ['timeout', 'connection', 'network']):
            logger.info(f"Retrying scraping task due to network error (attempt {retry_count + 2})")
            raise self.retry(countdown=180)  # 3 minutes

        return {
            'success': False,
            'error': str(exc),
            'retry_count': retry_count
        }

    finally:
        # Clean up scraper resources
        if scraper:
            scraper.cleanup()