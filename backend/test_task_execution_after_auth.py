#!/usr/bin/env python3

"""
Test task execution after authentication
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorAccount

def test_task_execution_after_auth():
    print("🚀 Task Execution After Authentication Test")
    print("=" * 60)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # 1. Check account authentication status
    print("\n🔐 1. Checking Account Authentication Status...")
    account = ActorAccount.objects.filter(user=user).first()
    if not account:
        print("❌ No account found")
        return
    
    print(f"   Account: @{account.platform_username} (ID: {account.id})")
    print(f"   Last login: {account.last_login}")
    print(f"   Session expires: {account.session_expires_at}")
    print(f"   Session valid: {account.is_session_valid()}")
    
    # 2. Get or create a pending task
    print("\n📋 2. Getting/Creating Pending Task...")
    task = ActorTask.objects.filter(user=user, status='PENDING').first()
    
    if not task:
        print("   Creating new task...")
        task = ActorTask.objects.create(
            user=user,
            actor_account=account,
            platform=account.platform,
            task_name='Post-Auth Execution Test',
            task_type='CONTENT_SEARCH',
            keywords='post auth test',
            max_items=3
        )
        print(f"   Created task: {task.task_name} (ID: {task.id})")
    else:
        print(f"   Using existing task: {task.task_name} (ID: {task.id})")
    
    print(f"   Task status: {task.status}")
    print(f"   Task type: {task.task_type}")
    print(f"   Account: @{task.actor_account.platform_username}")
    
    # 3. Test task execution
    print(f"\n🚀 3. Testing Task Execution...")
    execution_data = {
        'task_id': task.id
    }
    
    try:
        response = requests.post(
            f'{base_url}/actor/tasks/execute/', 
            headers=headers, 
            json=execution_data,
            timeout=15
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Task execution started successfully!")
                print(f"   Message: {result.get('message')}")
                
                # Check task status after execution
                task.refresh_from_db()
                print(f"   Updated task status: {task.status}")
                print(f"   Started at: {task.started_at}")
                print(f"   Progress: {task.progress_percentage}%")
                
            else:
                print(f"❌ Task execution failed: {result.get('error')}")
        else:
            try:
                error_data = response.json()
                print(f"❌ API request failed: {error_data}")
            except:
                print(f"❌ API request failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # 4. Check all tasks status
    print(f"\n📊 4. Checking All Tasks Status...")
    try:
        response = requests.get(f'{base_url}/actor/tasks/list/', headers=headers, timeout=10)
        if response.status_code == 200:
            tasks_result = response.json()
            if tasks_result.get('success'):
                tasks = tasks_result.get('tasks', [])
                print(f"✅ Found {len(tasks)} tasks")
                for t in tasks:
                    print(f"   - {t['name']} ({t['status']}) - Type: {t['type']}")
            else:
                print(f"❌ Failed to get tasks: {tasks_result.get('error')}")
        else:
            print(f"❌ Failed to get tasks: {response.text}")
    except Exception as e:
        print(f"❌ Error getting tasks: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Task Execution Test Complete!")

if __name__ == '__main__':
    test_task_execution_after_auth()
