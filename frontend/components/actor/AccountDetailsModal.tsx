'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
    User, Calendar, Clock, Shield, Activity, Eye, EyeOff,
    CheckCircle, XCircle, AlertCircle, RefreshCw, ExternalLink,
    Users, Video, Heart, MessageCircle, Share, TrendingUp,
    Plus, Play, Database, Search, Target, Settings
} from 'lucide-react';
import {
    getActorAccountDetails,
    authenticateActorAccount,
    createActorTask,
    executeActorTask,
    getActorScrapedData,
    type ActorAccount
} from '@/lib/api/actor-system';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface AccountDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    accountId: number | null;
    onAccountUpdate?: () => void;
}

interface AccountDetails extends ActorAccount {
    login_status: string;
    session_valid: boolean;
    session_expires_at: string | null;
    login_attempts: number;
    is_blocked: boolean;
    blocked_until: string | null;
    platform_info?: {
        profile_info?: {
            display_name: string;
            bio: string;
            profile_image: string | null;
            verified: boolean;
        };
        follower_count?: number;
        following_count?: number;
        video_count?: number;
        verification_status?: string;
        account_type?: string;
        last_activity?: string;
        platform_features?: {
            can_post: boolean;
            can_comment: boolean;
            can_message: boolean;
            analytics_available: boolean;
        };
    };
}

export function AccountDetailsModal({ isOpen, onClose, accountId, onAccountUpdate }: AccountDetailsModalProps) {
    const [account, setAccount] = useState<AccountDetails | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [authenticating, setAuthenticating] = useState(false);

    // Enhanced functionality state
    const [activeTab, setActiveTab] = useState('details');
    const [taskCreating, setTaskCreating] = useState(false);
    const [taskExecuting, setTaskExecuting] = useState(false);
    const [taskSuccess, setTaskSuccess] = useState<string | null>(null);
    const [scrapedData, setScrapedData] = useState<any[]>([]);
    const [realTimeData, setRealTimeData] = useState<any[]>([]);
    const [currentTaskId, setCurrentTaskId] = useState<number | null>(null);
    const [scrapingProgress, setScrapingProgress] = useState<string>('');
    const [isPolling, setIsPolling] = useState(false);

    // Task form state
    const [taskForm, setTaskForm] = useState({
        task_name: '',
        task_type: 'CONTENT_SEARCH',
        description: '',
        keywords: '',
        max_items: 10,
        start_date: '',
        end_date: '',
        region: 'global'
    });

    const loadAccountDetails = async () => {
        if (!accountId) return;
        
        try {
            setLoading(true);
            setError(null);
            
            const response = await getActorAccountDetails(accountId);
            if (response.success) {
                setAccount(response.account);
            } else {
                setError(response.error || 'Failed to load account details');
            }
        } catch (err: any) {
            setError(err.message || 'Failed to load account details');
        } finally {
            setLoading(false);
        }
    };

    const handleReauthenticate = async () => {
        if (!accountId) return;

        try {
            setAuthenticating(true);
            setError(null);

            // Show progress message
            const progressMessages = [
                'Starting authentication...',
                'Connecting to platform...',
                'Logging in (this may take up to 2 minutes)...',
                'Verifying credentials...',
                'Finalizing authentication...'
            ];

            let messageIndex = 0;
            const progressInterval = setInterval(() => {
                if (messageIndex < progressMessages.length - 1) {
                    messageIndex++;
                }
            }, 15000); // Update message every 15 seconds

            try {
                const response = await authenticateActorAccount(accountId);
                clearInterval(progressInterval);

                if (response.success) {
                    // Reload account details to get updated status
                    await loadAccountDetails();
                    if (onAccountUpdate) {
                        onAccountUpdate();
                    }
                } else {
                    setError(response.error || 'Authentication failed');
                }
            } catch (err: any) {
                clearInterval(progressInterval);

                if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
                    setError(
                        'Authentication is taking longer than expected. This is normal for real platform logins. ' +
                        'The process may still be running in the background. Please wait a few minutes and refresh the page to check the status.'
                    );
                } else {
                    setError(err.message || 'Authentication failed');
                }
            }
        } catch (err: any) {
            setError(err.message || 'Authentication failed');
        } finally {
            setAuthenticating(false);
        }
    };



    // Enhanced functionality: Create task directly in the modal
    const handleCreateTask = async () => {
        if (!accountId) return;

        try {
            setTaskCreating(true);
            setError(null);
            setTaskSuccess(null);

            const taskData = {
                account_id: accountId,
                task_type: taskForm.task_type,
                task_name: taskForm.task_name,
                description: taskForm.description,
                max_items: taskForm.max_items,
                keywords: taskForm.keywords,
                task_parameters: {
                    keywords: taskForm.keywords,
                    quality_filter: 'all',
                    include_metadata: true,
                    start_date: taskForm.start_date,
                    end_date: taskForm.end_date,
                    region: taskForm.region
                }
            };

            const response = await createActorTask(taskData);

            if (response.success) {
                setCurrentTaskId(response.task_id);
                setTaskSuccess(`Task "${taskForm.task_name}" created successfully! (ID: ${response.task_id})`);

                // Auto-execute the task if account is authenticated
                if (account?.session_valid) {
                    setScrapingProgress('Task created, starting execution...');
                    setTimeout(() => {
                        handleExecuteTask(response.task_id);
                    }, 1000);
                }

                // Reset form
                setTaskForm({
                    task_name: '',
                    task_type: 'CONTENT_SEARCH',
                    description: '',
                    keywords: '',
                    max_items: 10,
                    start_date: '',
                    end_date: '',
                    region: 'global'
                });
            } else {
                setError(response.error || 'Failed to create task');
            }
        } catch (err: any) {
            setError(err.message || 'Failed to create task');
        } finally {
            setTaskCreating(false);
        }
    };

    // Enhanced functionality: Execute task and show real-time results
    const handleExecuteTask = async (taskId: number) => {
        try {
            setTaskExecuting(true);
            setError(null);
            setCurrentTaskId(taskId);
            setScrapingProgress('Executing task...');

            const response = await executeActorTask(taskId);

            if (response.success) {
                setTaskSuccess(`Task execution started! ${response.message}`);
                setScrapingProgress(`Scraping in progress... Found ${response.items_scraped || 0} items so far`);

                // Start polling for real-time data
                startRealTimeDataPolling(taskId);

                // Switch to data tab to show results
                setActiveTab('data');
            } else {
                setError(response.error || 'Failed to execute task');
                setScrapingProgress('');
            }
        } catch (err: any) {
            setError(err.message || 'Failed to execute task');
            setScrapingProgress('');
        } finally {
            setTaskExecuting(false);
        }
    };

    // Real-time data polling with progress tracking
    const startRealTimeDataPolling = (taskId?: number) => {
        setIsPolling(true);
        let pollCount = 0;
        const maxPolls = 24; // 2 minutes of polling (5 seconds * 24)

        const pollData = async () => {
            try {
                const response = await getActorScrapedData({
                    account_id: accountId,
                    task_id: taskId,
                    page_size: 20
                });

                if (response.success) {
                    const newData = response.results || [];
                    setRealTimeData(newData);
                    setScrapedData(newData);

                    // Update progress message
                    if (newData.length > 0) {
                        setScrapingProgress(`✅ Scraping complete! Found ${newData.length} items`);
                        setIsPolling(false);
                        return true; // Stop polling
                    } else {
                        pollCount++;
                        setScrapingProgress(`🔄 Scraping in progress... (${pollCount}/${maxPolls}) Checking for new data...`);
                    }
                }
            } catch (err) {
                console.warn('Failed to poll data:', err);
                setScrapingProgress('⚠️ Error checking for new data');
            }
            return false;
        };

        // Poll immediately and then every 5 seconds
        const runPolling = async () => {
            const shouldStop = await pollData();
            if (shouldStop) return;

            const interval = setInterval(async () => {
                pollCount++;
                const shouldStop = await pollData();

                if (shouldStop || pollCount >= maxPolls) {
                    clearInterval(interval);
                    setIsPolling(false);
                    if (pollCount >= maxPolls) {
                        setScrapingProgress('⏱️ Polling timeout reached. Check the Data tab for any results.');
                    }
                }
            }, 5000);
        };

        runPolling();
    };

    useEffect(() => {
        if (isOpen && accountId) {
            loadAccountDetails();
            // Load existing scraped data
            loadScrapedData();
        }
    }, [isOpen, accountId]);

    const loadScrapedData = async () => {
        if (!accountId) return;

        try {
            const response = await getActorScrapedData({
                account_id: accountId,
                page_size: 10
            });

            if (response.success) {
                setScrapedData(response.results || []);
            }
        } catch (err) {
            console.warn('Failed to load scraped data:', err);
        }
    };

    const getLoginStatusColor = (status: string) => {
        switch (status) {
            case 'authenticated':
                return 'bg-green-100 text-green-800';
            case 'session_expired':
                return 'bg-yellow-100 text-yellow-800';
            case 'never_logged_in':
                return 'bg-gray-100 text-gray-800';
            case 'blocked':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getLoginStatusIcon = (status: string) => {
        switch (status) {
            case 'authenticated':
                return <CheckCircle className="w-4 h-4" />;
            case 'session_expired':
                return <Clock className="w-4 h-4" />;
            case 'never_logged_in':
                return <EyeOff className="w-4 h-4" />;
            case 'blocked':
                return <XCircle className="w-4 h-4" />;
            default:
                return <AlertCircle className="w-4 h-4" />;
        }
    };

    const getLoginStatusText = (status: string) => {
        switch (status) {
            case 'authenticated':
                return 'Authenticated';
            case 'session_expired':
                return 'Session Expired';
            case 'never_logged_in':
                return 'Never Logged In';
            case 'blocked':
                return 'Blocked';
            default:
                return 'Unknown';
        }
    };

    const getPlatformColor = (platform: string) => {
        switch (platform.toLowerCase()) {
            case 'tiktok':
                return 'bg-black text-white';
            case 'instagram':
                return 'bg-pink-500 text-white';
            case 'facebook':
                return 'bg-blue-600 text-white';
            case 'twitter':
                return 'bg-blue-400 text-white';
            case 'youtube':
                return 'bg-red-600 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <User className="w-5 h-5" />
                        Account Details
                    </DialogTitle>
                </DialogHeader>

                {error && (
                    <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}

                {loading ? (
                    <div className="space-y-4">
                        <Skeleton className="h-32 w-full" />
                        <Skeleton className="h-24 w-full" />
                        <Skeleton className="h-24 w-full" />
                    </div>
                ) : account ? (
                    <div className="space-y-6">
                        {/* Success Messages */}
                        {taskSuccess && (
                            <Alert>
                                <CheckCircle className="h-4 w-4" />
                                <AlertDescription>{taskSuccess}</AlertDescription>
                            </Alert>
                        )}

                        {/* Enhanced Tabbed Interface */}
                        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                                <TabsTrigger value="details">
                                    <User className="w-4 h-4 mr-2" />
                                    Account Details
                                </TabsTrigger>
                                <TabsTrigger value="create-task" disabled={!account.session_valid}>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Task
                                </TabsTrigger>
                                <TabsTrigger value="data">
                                    <Database className="w-4 h-4 mr-2" />
                                    Scraped Data ({scrapedData.length})
                                </TabsTrigger>
                                <TabsTrigger value="settings">
                                    <Settings className="w-4 h-4 mr-2" />
                                    Settings
                                </TabsTrigger>
                            </TabsList>

                            {/* Account Details Tab */}
                            <TabsContent value="details" className="space-y-4">
                                {/* Keep existing account details content here */}
                        {/* Basic Account Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <User className="w-5 h-5" />
                                        Account Information
                                    </div>
                                    <div className="flex gap-2">
                                        <Badge className={getPlatformColor(account.platform)}>
                                            {account.platform}
                                        </Badge>
                                        <Badge className={getLoginStatusColor(account.login_status)}>
                                            {getLoginStatusIcon(account.login_status)}
                                            <span className="ml-1">{getLoginStatusText(account.login_status)}</span>
                                        </Badge>
                                    </div>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Username</label>
                                        <p className="text-lg font-semibold">@{account.username}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Email</label>
                                        <p>{account.email || 'Not provided'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Created</label>
                                        <p>{new Date(account.created_at).toLocaleString()}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Last Login</label>
                                        <p>{account.last_login ? new Date(account.last_login).toLocaleString() : 'Never'}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Session Status */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Shield className="w-5 h-5" />
                                    Session Status
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Session Valid</label>
                                        <p className="flex items-center gap-2">
                                            {account.session_valid ? (
                                                <>
                                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                                    <span className="text-green-600">Valid</span>
                                                </>
                                            ) : (
                                                <>
                                                    <XCircle className="w-4 h-4 text-red-600" />
                                                    <span className="text-red-600">Invalid</span>
                                                </>
                                            )}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Session Expires</label>
                                        <p>{account.session_expires_at ? new Date(account.session_expires_at).toLocaleString() : 'Not set'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Login Attempts</label>
                                        <p>{account.login_attempts}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Account Status</label>
                                        <p className="flex items-center gap-2">
                                            {account.is_blocked ? (
                                                <>
                                                    <XCircle className="w-4 h-4 text-red-600" />
                                                    <span className="text-red-600">Blocked</span>
                                                </>
                                            ) : (
                                                <>
                                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                                    <span className="text-green-600">Active</span>
                                                </>
                                            )}
                                        </p>
                                    </div>
                                </div>

                                {!account.session_valid && (
                                    <div className="mt-4 space-y-3">
                                        <Button
                                            onClick={handleReauthenticate}
                                            disabled={authenticating}
                                            className="w-full"
                                        >
                                            {authenticating ? (
                                                <>
                                                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                    Authenticating...
                                                </>
                                            ) : (
                                                <>
                                                    <Shield className="w-4 h-4 mr-2" />
                                                    Authentication
                                                </>
                                            )}
                                        </Button>

                                        {authenticating && (
                                            <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                                <p className="text-sm text-blue-800">
                                                    <strong>Please wait:</strong> Authenticating with {account.platform} platform...
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Platform Info */}
                        {account.platform_info && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="w-5 h-5" />
                                        Platform Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {account.platform_info.profile_info && (
                                        <div className="mb-4">
                                            <h4 className="font-medium mb-2">Profile</h4>
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <label className="text-sm font-medium text-gray-600">Display Name</label>
                                                    <p>{account.platform_info.profile_info.display_name}</p>
                                                </div>
                                                <div>
                                                    <label className="text-sm font-medium text-gray-600">Verified</label>
                                                    <p>{account.platform_info.profile_info.verified ? 'Yes' : 'No'}</p>
                                                </div>
                                            </div>
                                            {account.platform_info.profile_info.bio && (
                                                <div className="mt-2">
                                                    <label className="text-sm font-medium text-gray-600">Bio</label>
                                                    <p className="text-sm text-gray-700">{account.platform_info.profile_info.bio}</p>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    <div className="grid grid-cols-3 gap-4">
                                        <div className="text-center">
                                            <Users className="w-6 h-6 mx-auto text-blue-600 mb-1" />
                                            <p className="text-2xl font-bold">{account.platform_info.follower_count || 0}</p>
                                            <p className="text-sm text-gray-600">Followers</p>
                                        </div>
                                        <div className="text-center">
                                            <Heart className="w-6 h-6 mx-auto text-pink-600 mb-1" />
                                            <p className="text-2xl font-bold">{account.platform_info.following_count || 0}</p>
                                            <p className="text-sm text-gray-600">Following</p>
                                        </div>
                                        <div className="text-center">
                                            <Video className="w-6 h-6 mx-auto text-purple-600 mb-1" />
                                            <p className="text-2xl font-bold">{account.platform_info.video_count || 0}</p>
                                            <p className="text-sm text-gray-600">Videos</p>
                                        </div>
                                    </div>

                                    {account.platform_info.platform_features && (
                                        <div className="mt-4">
                                            <h4 className="font-medium mb-2">Platform Features</h4>
                                            <div className="grid grid-cols-2 gap-2">
                                                <div className="flex items-center gap-2">
                                                    {account.platform_info.platform_features.can_post ? (
                                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                                    ) : (
                                                        <XCircle className="w-4 h-4 text-red-600" />
                                                    )}
                                                    <span className="text-sm">Can Post</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {account.platform_info.platform_features.can_comment ? (
                                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                                    ) : (
                                                        <XCircle className="w-4 h-4 text-red-600" />
                                                    )}
                                                    <span className="text-sm">Can Comment</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {account.platform_info.platform_features.can_message ? (
                                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                                    ) : (
                                                        <XCircle className="w-4 h-4 text-red-600" />
                                                    )}
                                                    <span className="text-sm">Can Message</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {account.platform_info.platform_features.analytics_available ? (
                                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                                    ) : (
                                                        <XCircle className="w-4 h-4 text-red-600" />
                                                    )}
                                                    <span className="text-sm">Analytics</span>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}
                            </TabsContent>

                            {/* Create Task Tab */}
                            <TabsContent value="create-task" className="space-y-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Plus className="w-5 h-5" />
                                            Create New Task
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="text-sm font-medium">Task Name</label>
                                                <Input
                                                    value={taskForm.task_name}
                                                    onChange={(e) => setTaskForm({...taskForm, task_name: e.target.value})}
                                                    placeholder="Enter task name..."
                                                />
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium">Task Type</label>
                                                <Select value={taskForm.task_type} onValueChange={(value) => setTaskForm({...taskForm, task_type: value})}>
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="CONTENT_SEARCH">Content Search</SelectItem>
                                                        <SelectItem value="PROFILE_SCRAPING">Profile Scraping</SelectItem>
                                                        <SelectItem value="USER_CONTENT">User Content</SelectItem>
                                                        <SelectItem value="KEYWORD_MONITORING">Keyword Monitoring</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>

                                        <div>
                                            <label className="text-sm font-medium">Description</label>
                                            <Textarea
                                                value={taskForm.description}
                                                onChange={(e) => setTaskForm({...taskForm, description: e.target.value})}
                                                placeholder="Describe what this task should do..."
                                                rows={3}
                                            />
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="text-sm font-medium">Keywords</label>
                                                <Input
                                                    value={taskForm.keywords}
                                                    onChange={(e) => setTaskForm({...taskForm, keywords: e.target.value})}
                                                    placeholder="Enter keywords..."
                                                />
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium">Max Items</label>
                                                <Input
                                                    type="number"
                                                    value={taskForm.max_items}
                                                    onChange={(e) => setTaskForm({...taskForm, max_items: parseInt(e.target.value) || 10})}
                                                    min="1"
                                                    max="100"
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="text-sm font-medium">Start Date (Optional)</label>
                                                <Input
                                                    type="date"
                                                    value={taskForm.start_date}
                                                    onChange={(e) => setTaskForm({...taskForm, start_date: e.target.value})}
                                                />
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium">End Date (Optional)</label>
                                                <Input
                                                    type="date"
                                                    value={taskForm.end_date}
                                                    onChange={(e) => setTaskForm({...taskForm, end_date: e.target.value})}
                                                />
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium">Region</label>
                                                <Select value={taskForm.region} onValueChange={(value) => setTaskForm({...taskForm, region: value})}>
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="global">🌍 Global</SelectItem>
                                                        <SelectItem value="indonesia">🇮🇩 Indonesia</SelectItem>
                                                        <SelectItem value="asia">🌏 Asia Pacific</SelectItem>
                                                        <SelectItem value="europe">🇪🇺 Europe</SelectItem>
                                                        <SelectItem value="americas">🌎 Americas</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Filter content by geographic region for more relevant results
                                                </p>
                                            </div>
                                        </div>

                                        <div className="flex gap-2 pt-4">
                                            <Button
                                                onClick={handleCreateTask}
                                                disabled={taskCreating || !taskForm.task_name || !taskForm.keywords}
                                                className="flex-1"
                                            >
                                                {taskCreating ? (
                                                    <>
                                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                        Creating Task...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Plus className="w-4 h-4 mr-2" />
                                                        Create & Execute Task
                                                    </>
                                                )}
                                            </Button>
                                        </div>

                                        {account?.session_valid && (
                                            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                                                <p className="text-sm text-green-800">
                                                    ✅ <strong>Ready to scrape!</strong> Your account is authenticated and the task will be executed immediately after creation.
                                                </p>
                                            </div>
                                        )}

                                        {/* Task Execution Progress */}
                                        {(taskExecuting || isPolling || scrapingProgress) && (
                                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                                                <div className="flex items-center gap-2 mb-2">
                                                    {(taskExecuting || isPolling) && (
                                                        <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                                                    )}
                                                    <span className="font-medium text-blue-900">
                                                        {taskExecuting ? 'Executing Task...' : isPolling ? 'Scraping in Progress...' : 'Task Status'}
                                                    </span>
                                                </div>
                                                {scrapingProgress && (
                                                    <p className="text-sm text-blue-800">{scrapingProgress}</p>
                                                )}
                                                {currentTaskId && (
                                                    <p className="text-xs text-blue-600 mt-1">Task ID: {currentTaskId}</p>
                                                )}
                                                {isPolling && (
                                                    <div className="mt-2">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => setActiveTab('data')}
                                                        >
                                                            <Database className="w-4 h-4 mr-1" />
                                                            View Data Tab
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Scraped Data Tab */}
                            <TabsContent value="data" className="space-y-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <Database className="w-5 h-5" />
                                                Scraped Data
                                                {isPolling && (
                                                    <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Badge variant="secondary">
                                                    {scrapedData.length} items
                                                </Badge>
                                                {currentTaskId && (
                                                    <Badge variant="outline">
                                                        Task #{currentTaskId}
                                                    </Badge>
                                                )}
                                            </div>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {/* Real-time Status */}
                                        {isPolling && (
                                            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                                <div className="flex items-center gap-2">
                                                    <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                                                    <span className="font-medium text-blue-900">Live Data Updates</span>
                                                </div>
                                                <p className="text-sm text-blue-800 mt-1">
                                                    {scrapingProgress || 'Checking for new scraped data...'}
                                                </p>
                                            </div>
                                        )}
                                        {scrapedData.length === 0 ? (
                                            <div className="text-center py-8">
                                                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                                <h3 className="text-lg font-medium text-gray-900 mb-2">No data yet</h3>
                                                <p className="text-gray-500">
                                                    Create and execute a task to see scraped data here.
                                                </p>
                                            </div>
                                        ) : (
                                            <div className="space-y-3 max-h-96 overflow-y-auto">
                                                {scrapedData.map((item, index) => (
                                                    <div key={index} className="p-3 border rounded-lg">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <Badge variant="outline">
                                                                {item.data_type}
                                                            </Badge>
                                                            <span className="text-xs text-gray-500">
                                                                {new Date(item.scraped_at).toLocaleString()}
                                                            </span>
                                                        </div>
                                                        <div className="text-sm">
                                                            <strong>Quality:</strong> {item.quality_score?.toFixed(1) || 'N/A'}
                                                        </div>
                                                        <div className="text-sm">
                                                            <strong>Content ID:</strong> {item.platform_content_id || 'N/A'}
                                                        </div>
                                                        {item.content && (
                                                            <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                                                                <pre className="whitespace-pre-wrap">
                                                                    {JSON.stringify(item.content, null, 2).substring(0, 200)}
                                                                    {JSON.stringify(item.content, null, 2).length > 200 ? '...' : ''}
                                                                </pre>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Settings Tab */}
                            <TabsContent value="settings" className="space-y-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Settings className="w-5 h-5" />
                                            Account Settings
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex gap-2">
                                            <Button onClick={loadAccountDetails} variant="outline">
                                                <RefreshCw className="w-4 h-4 mr-2" />
                                                Refresh Account
                                            </Button>
                                            <Button onClick={() => setActiveTab('details')} variant="outline">
                                                <User className="w-4 h-4 mr-2" />
                                                View Details
                                            </Button>
                                        </div>

                                        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                                            <h4 className="font-medium text-blue-900 mb-2">Enhanced Workflow</h4>
                                            <p className="text-sm text-blue-800">
                                                This enhanced account modal allows you to authenticate, create tasks, and view scraped data all in one place.
                                                Keep this window open to maintain your session and see real-time scraping results!
                                            </p>
                                        </div>
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>

                        <div className="flex gap-2 pt-4">
                            <Button onClick={onClose} variant="outline" className="ml-auto">
                                Close
                            </Button>
                        </div>
                    </div>
                ) : null}
            </DialogContent>
        </Dialog>
    );
}
